definitions:
  nunu-layout-admin_api_v1.AdminUserCreateRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      nickname:
        example: 小Baby
        type: string
      password:
        example: "123456"
        type: string
      phone:
        example: "1858888888"
        type: string
      roles:
        example:
        - ""
        items:
          type: string
        type: array
      username:
        example: 张三
        type: string
    required:
    - password
    - username
    type: object
  nunu-layout-admin_api_v1.AdminUserDataItem:
    properties:
      createdAt:
        type: string
      email:
        example: <EMAIL>
        type: string
      id:
        type: integer
      nickname:
        example: 小Baby
        type: string
      password:
        example: "123456"
        type: string
      phone:
        example: "1858888888"
        type: string
      roles:
        example:
        - ""
        items:
          type: string
        type: array
      updatedAt:
        type: string
      username:
        example: 张三
        type: string
    required:
    - email
    - nickname
    - password
    - username
    type: object
  nunu-layout-admin_api_v1.AdminUserUpdateRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      id:
        type: integer
      nickname:
        example: 小Baby
        type: string
      password:
        example: "123456"
        type: string
      phone:
        example: "1858888888"
        type: string
      roles:
        example:
        - ""
        items:
          type: string
        type: array
      username:
        example: 张三
        type: string
    required:
    - username
    type: object
  nunu-layout-admin_api_v1.ApiCreateRequest:
    properties:
      group:
        example: 权限管理
        type: string
      method:
        example: GET
        type: string
      name:
        example: 菜单列表
        type: string
      path:
        example: /v1/test
        type: string
    type: object
  nunu-layout-admin_api_v1.ApiDataItem:
    properties:
      createdAt:
        type: string
      group:
        type: string
      id:
        type: integer
      method:
        type: string
      name:
        type: string
      path:
        type: string
      updatedAt:
        type: string
    type: object
  nunu-layout-admin_api_v1.ApiUpdateRequest:
    properties:
      group:
        example: 权限管理
        type: string
      id:
        example: 1
        type: integer
      method:
        example: GET
        type: string
      name:
        example: 菜单列表
        type: string
      path:
        example: /v1/test
        type: string
    required:
    - id
    type: object
  nunu-layout-admin_api_v1.GetAdminUserResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/nunu-layout-admin_api_v1.GetAdminUserResponseData'
      message:
        type: string
    type: object
  nunu-layout-admin_api_v1.GetAdminUserResponseData:
    properties:
      createdAt:
        type: string
      email:
        example: <EMAIL>
        type: string
      id:
        type: integer
      nickname:
        example: 小Baby
        type: string
      password:
        example: "123456"
        type: string
      phone:
        example: "1858888888"
        type: string
      roles:
        example:
        - ""
        items:
          type: string
        type: array
      updatedAt:
        type: string
      username:
        example: 张三
        type: string
    type: object
  nunu-layout-admin_api_v1.GetAdminUsersResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/nunu-layout-admin_api_v1.GetAdminUsersResponseData'
      message:
        type: string
    type: object
  nunu-layout-admin_api_v1.GetAdminUsersResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/nunu-layout-admin_api_v1.AdminUserDataItem'
        type: array
      total:
        type: integer
    type: object
  nunu-layout-admin_api_v1.GetApisResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/nunu-layout-admin_api_v1.GetApisResponseData'
      message:
        type: string
    type: object
  nunu-layout-admin_api_v1.GetApisResponseData:
    properties:
      groups:
        items:
          type: string
        type: array
      list:
        items:
          $ref: '#/definitions/nunu-layout-admin_api_v1.ApiDataItem'
        type: array
      total:
        type: integer
    type: object
  nunu-layout-admin_api_v1.GetMenuResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/nunu-layout-admin_api_v1.GetMenuResponseData'
      message:
        type: string
    type: object
  nunu-layout-admin_api_v1.GetMenuResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/nunu-layout-admin_api_v1.MenuDataItem'
        type: array
    type: object
  nunu-layout-admin_api_v1.GetRolePermissionsData:
    properties:
      list:
        items:
          type: string
        type: array
    type: object
  nunu-layout-admin_api_v1.GetRolesResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/nunu-layout-admin_api_v1.GetRolesResponseData'
      message:
        type: string
    type: object
  nunu-layout-admin_api_v1.GetRolesResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/nunu-layout-admin_api_v1.RoleDataItem'
        type: array
      total:
        type: integer
    type: object
  nunu-layout-admin_api_v1.GetUserPermissionsData:
    properties:
      list:
        items:
          type: string
        type: array
    type: object
  nunu-layout-admin_api_v1.LoginRequest:
    properties:
      password:
        example: "123456"
        type: string
      username:
        example: <EMAIL>
        type: string
    required:
    - password
    - username
    type: object
  nunu-layout-admin_api_v1.LoginResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/nunu-layout-admin_api_v1.LoginResponseData'
      message:
        type: string
    type: object
  nunu-layout-admin_api_v1.LoginResponseData:
    properties:
      accessToken:
        type: string
    type: object
  nunu-layout-admin_api_v1.MenuCreateRequest:
    properties:
      component:
        description: 绑定的组件
        type: string
      hideInMenu:
        description: 是否保活
        type: boolean
      icon:
        description: 图标，使用字符串表示
        type: string
      keepAlive:
        description: 是否保活
        type: boolean
      locale:
        description: 本地化标识
        type: string
      name:
        description: 同路由中的name，唯一标识
        type: string
      parentId:
        description: 父级菜单的id，使用整数表示
        type: integer
      path:
        description: 地址
        type: string
      redirect:
        description: 重定向地址
        type: string
      title:
        description: 展示名称
        type: string
      url:
        description: iframe模式下的跳转url，不能与path重复
        type: string
      weight:
        description: 排序权重
        type: integer
    type: object
  nunu-layout-admin_api_v1.MenuDataItem:
    properties:
      component:
        description: 绑定的组件
        type: string
      hideInMenu:
        description: 是否保活
        type: boolean
      icon:
        description: 图标，使用字符串表示
        type: string
      id:
        description: 唯一id，使用整数表示
        type: integer
      keepAlive:
        description: 是否保活
        type: boolean
      locale:
        description: 本地化标识
        type: string
      name:
        description: 同路由中的name，唯一标识
        type: string
      parentId:
        description: 父级菜单的id，使用整数表示
        type: integer
      path:
        description: 地址
        type: string
      redirect:
        description: 重定向地址
        type: string
      title:
        description: 展示名称
        type: string
      updatedAt:
        description: 是否保活
        type: string
      url:
        description: iframe模式下的跳转url，不能与path重复
        type: string
      weight:
        description: 排序权重
        type: integer
    type: object
  nunu-layout-admin_api_v1.MenuUpdateRequest:
    properties:
      component:
        description: 绑定的组件
        type: string
      hideInMenu:
        description: 是否保活
        type: boolean
      icon:
        description: 图标，使用字符串表示
        type: string
      id:
        description: 唯一id，使用整数表示
        type: integer
      keepAlive:
        description: 是否保活
        type: boolean
      locale:
        description: 本地化标识
        type: string
      name:
        description: 同路由中的name，唯一标识
        type: string
      parentId:
        description: 父级菜单的id，使用整数表示
        type: integer
      path:
        description: 地址
        type: string
      redirect:
        description: 重定向地址
        type: string
      title:
        description: 展示名称
        type: string
      updatedAt:
        type: string
      url:
        description: iframe模式下的跳转url，不能与path重复
        type: string
      weight:
        description: 排序权重
        type: integer
    type: object
  nunu-layout-admin_api_v1.Response:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  nunu-layout-admin_api_v1.RoleCreateRequest:
    properties:
      name:
        example: Admin
        type: string
      sid:
        example: "1"
        type: string
    required:
    - name
    - sid
    type: object
  nunu-layout-admin_api_v1.RoleDataItem:
    properties:
      createdAt:
        type: string
      id:
        type: integer
      name:
        type: string
      sid:
        type: string
      updatedAt:
        type: string
    type: object
  nunu-layout-admin_api_v1.RoleUpdateRequest:
    properties:
      id:
        example: 1
        type: integer
      name:
        example: Admin
        type: string
      sid:
        example: "1"
        type: string
    required:
    - id
    - name
    - sid
    type: object
  nunu-layout-admin_api_v1.UpdateRolePermissionRequest:
    properties:
      list:
        example:
        - ""
        items:
          type: string
        type: array
      role:
        example: admin
        type: string
    required:
    - list
    - role
    type: object
host: localhost:8000
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a sample server celler server.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Nunu Example API
  version: 1.0.0
paths:
  /v1/admin/api:
    delete:
      consumes:
      - application/json
      description: 删除指定API
      parameters:
      - description: API ID
        in: query
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除API
      tags:
      - API模块
    post:
      consumes:
      - application/json
      description: 创建新的API
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/nunu-layout-admin_api_v1.ApiCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建API
      tags:
      - API模块
    put:
      consumes:
      - application/json
      description: 更新API信息
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/nunu-layout-admin_api_v1.ApiUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新API
      tags:
      - API模块
  /v1/admin/apis:
    get:
      consumes:
      - application/json
      description: 获取API列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页数量
        in: query
        name: pageSize
        required: true
        type: integer
      - description: API分组
        in: query
        name: group
        type: string
      - description: API名称
        in: query
        name: name
        type: string
      - description: API路径
        in: query
        name: path
        type: string
      - description: 请求方法
        in: query
        name: method
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.GetApisResponse'
      security:
      - Bearer: []
      summary: 获取API列表
      tags:
      - API模块
  /v1/admin/menu:
    delete:
      consumes:
      - application/json
      description: 删除指定菜单
      parameters:
      - description: 菜单ID
        in: query
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除菜单
      tags:
      - 菜单模块
    post:
      consumes:
      - application/json
      description: 创建新的菜单
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/nunu-layout-admin_api_v1.MenuCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建菜单
      tags:
      - 菜单模块
    put:
      consumes:
      - application/json
      description: 更新菜单信息
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/nunu-layout-admin_api_v1.MenuUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新菜单
      tags:
      - 菜单模块
  /v1/admin/menus:
    get:
      consumes:
      - application/json
      description: 获取管理员菜单列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.GetMenuResponse'
      security:
      - Bearer: []
      summary: 获取管理员菜单
      tags:
      - 菜单模块
  /v1/admin/role:
    delete:
      consumes:
      - application/json
      description: 删除指定角色
      parameters:
      - description: 角色ID
        in: query
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除角色
      tags:
      - 角色模块
    post:
      consumes:
      - application/json
      description: 创建新的角色
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/nunu-layout-admin_api_v1.RoleCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建角色
      tags:
      - 角色模块
    put:
      consumes:
      - application/json
      description: 更新角色信息
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/nunu-layout-admin_api_v1.RoleUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新角色
      tags:
      - 角色模块
  /v1/admin/role/permissions:
    get:
      consumes:
      - application/json
      description: 获取指定角色的权限列表
      parameters:
      - description: 角色名称
        in: query
        name: role
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.GetRolePermissionsData'
      security:
      - Bearer: []
      summary: 获取角色权限
      tags:
      - 权限模块
    put:
      consumes:
      - application/json
      description: 更新指定角色的权限列表
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/nunu-layout-admin_api_v1.UpdateRolePermissionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新角色权限
      tags:
      - 权限模块
  /v1/admin/roles:
    get:
      consumes:
      - application/json
      description: 获取角色列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页数量
        in: query
        name: pageSize
        required: true
        type: integer
      - description: 角色ID
        in: query
        name: sid
        type: string
      - description: 角色名称
        in: query
        name: name
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.GetRolesResponse'
      security:
      - Bearer: []
      summary: 获取角色列表
      tags:
      - 角色模块
  /v1/admin/user:
    delete:
      consumes:
      - application/json
      description: 删除指定管理员用户
      parameters:
      - description: 用户ID
        in: query
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除管理员用户
      tags:
      - 用户模块
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.GetAdminUserResponse'
      security:
      - Bearer: []
      summary: 获取管理用户信息
      tags:
      - 用户模块
    post:
      consumes:
      - application/json
      description: 创建新的管理员用户
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/nunu-layout-admin_api_v1.AdminUserCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建管理员用户
      tags:
      - 用户模块
    put:
      consumes:
      - application/json
      description: 更新管理员用户信息
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/nunu-layout-admin_api_v1.AdminUserUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新管理员用户
      tags:
      - 用户模块
  /v1/admin/user/permissions:
    get:
      consumes:
      - application/json
      description: 获取当前用户的权限列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.GetUserPermissionsData'
      security:
      - Bearer: []
      summary: 获取用户权限
      tags:
      - 权限模块
  /v1/admin/users:
    get:
      consumes:
      - application/json
      description: 获取管理员用户列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页数量
        in: query
        name: pageSize
        required: true
        type: integer
      - description: 用户名
        in: query
        name: username
        type: string
      - description: 昵称
        in: query
        name: nickname
        type: string
      - description: 手机号
        in: query
        name: phone
        type: string
      - description: 邮箱
        in: query
        name: email
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.GetAdminUsersResponse'
      security:
      - Bearer: []
      summary: 获取管理员用户列表
      tags:
      - 用户模块
  /v1/login:
    post:
      consumes:
      - application/json
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/nunu-layout-admin_api_v1.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.LoginResponse'
      summary: 账号登录
      tags:
      - 用户模块
  /v1/menus:
    get:
      consumes:
      - application/json
      description: 获取当前用户的菜单列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/nunu-layout-admin_api_v1.GetMenuResponse'
      security:
      - Bearer: []
      summary: 获取用户菜单
      tags:
      - 菜单模块
securityDefinitions:
  Bearer:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
