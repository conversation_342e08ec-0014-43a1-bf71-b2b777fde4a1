package v1

type saveMessageWorkRequest struct {
	Url  string `json:"url"`
	Sort string `json:"sort"`
}

type saveMessageWorkResponse struct {
	Response
}

// 定义创建请求模型
type CreateMessageWorkRequest struct {
	Url  string `json:"url"`                            // 必填字段，验证URL格式
	Sort int    `json:"sort" binding:"omitempty,min=0"` // 选填字段，最小值0
}

type UpdateMessageWorkRequest struct {
	Id   int    `json:"id"`
	Url  string `json:"url"`
	Sort int    `json:"sort" binding:"omitempty,min=0"`
}
type DeleteMessageWorkRequest struct {
	Id int `json:"id" `
}

type ListMessageWorkRequest struct {
	Page     int `form:"page" binding:"omitempty,min=1"`
	PageSize int `form:"pageSize" binding:"omitempty,min=1,max=100"`
	// 可添加更多查询条件，如关键词搜索等
}

type MessageWorkItem struct {
	Id   int    `json:"id"`
	Url  string `json:"url"`
	Sort int    `json:"sort"`
}

type ListMessageWorkResponse struct {
	Total int64              `json:"total"`
	List  []*MessageWorkItem `json:"list"`
}

type MessageWorkDetailRequest struct {
	ID uint `uri:"id" binding:"required,min=1"` // ID从URL路径中获取
}

type MessageWorkDetailResponse struct {
	ID   uint   `json:"id"`
	Url  string `json:"url"`
	Sort int    `json:"sort"`
}
