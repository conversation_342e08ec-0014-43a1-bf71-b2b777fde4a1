package v1

type SaveCategoryRequest struct {
	TypeName string `json:"type_name" binding:"required" `
	Sort     int    `json:"sort" binding:"required"`
}
type UpdateCategoryRequest struct {
	Id       int    `form:"id" binding:"required"`
	TypeName string `json:"type_name" binding:"required" `
	Sort     int    `json:"sort" binding:"required"`
}

type GetCategoryRequest struct {
	ID int64 `uri:"id" binding:"required,min=1"` // ID从URL路径中获取
}

type GetCategoryResponse struct {
	ID       int64  `json:"id"`
	TypeName string `json:"type_name"`
	Sort     int    `json:"sort"`
}

type ListCategoryRequest struct {
	Page     int `form:"page" binding:"required,min=1"`     // 页码
	PageSize int `form:"pageSize" binding:"required,min=1"` // 每页数量

}
type DeleteCategoryRequest struct {
	Id int `form:"id" binding:"required"`
}

// MessageIndexListResponse 列表查询响应
type ListCategoryResponse struct {
	Total int64           `json:"total"` // 总记录数
	List  []*CategoryItem `json:"list"`  // 数据列表
}

type CategoryItem struct {
	ID       uint   `json:"id"`
	TypeName string `json:"type_name"`
	Sort     int    `json:"sort"`
}

type saveTypeResponse struct {
	Response
}
