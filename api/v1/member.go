package v1

type SaveMemberRequest struct {
	Avatar     string `json:"avatar" `
	Name       string `json:"name" `
	NameCn     string `json:"name_cn" `
	Position   string `json:"position"`
	PositionCn string `json:"position_cn" `
	BioEn      string `json:"bio_en" `
	BioCn      string `json:"bio_cn"`
}

type UpdateMemberRequest struct {
	Id         int    `json:"id" binding:"required"`
	Avatar     string `json:"avatar" `
	Name       string `json:"name" `
	NameCn     string `json:"name_cn" `
	Position   string `json:"position" `
	PositionCn string `json:"position_cn" `
	BioEn      string `json:"bio_en" `
	BioCn      string `json:"bio_cn" `
}

type GetMemberRequest struct {
	ID int64 `uri:"id" binding:"required,min=1"` // ID从URL路径中获取
}

type GetMemberResponse struct {
	ID         int64  `json:"id"`
	Avatar     string `json:"avatar"`
	Name       string `json:"name"`
	NameCn     string `json:"name_cn"`
	Position   string `json:"position"`
	PositionCn string `json:"position_cn"`
	BioEn      string `json:"bio_en"`
	BioCn      string `json:"bio_cn"`
}

type ListMemberRequest struct {
	Page     int `form:"page" binding:"required,min=1"`     // 页码
	PageSize int `form:"pageSize" binding:"required,min=1"` // 每页数量
}

type DeleteMemberRequest struct {
	Id int `form:"id" binding:"required"`
}

type ListMemberResponse struct {
	Total int64         `json:"total"` // 总记录数
	List  []*MemberItem `json:"list"`  // 数据列表
}

type MemberItem struct {
	ID         uint   `json:"id"`
	Avatar     string `json:"avatar"`
	Name       string `json:"name"`
	NameCn     string `json:"name_cn"`
	Position   string `json:"position"`
	PositionCn string `json:"position_cn"`
	BioEn      string `json:"bio_en"`
	BioCn      string `json:"bio_cn"`
}
