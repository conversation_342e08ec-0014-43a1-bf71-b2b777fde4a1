package v1

type CreateOrUpdateDetailRequest struct {
	IndexId int              `json:"index_id"  `
	ImgArr  []*ImgArrRequest `json:"img_arr"`
}

type ImgArrRequest struct {
	Sort int    `json:"sort"  `
	Img  string `json:"img"  `
}

type MessageIndexImgResponse struct {
	IndexId int                    `json:"index_id"` // 数据id
	ImgArr  []*MessageIndexImgItem `json:"img_arr"`  // 数据列表
}

type MessageIndexImgItem struct {
	Sort int    `json:"sort"`
	Img  string `json:"img"`
}
type GetDetailRequest struct {
	IndexId int `json:"index_id"  `
}
