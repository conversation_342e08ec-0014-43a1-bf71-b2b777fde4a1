package v1

import "html/template"

type SaveMessageIndexRequest struct {
	Project      string `json:"project" `
	Type         string `json:"type" `
	Client       string `json:"client" `
	Year         int    `json:"year"`
	ChTitle      string `json:"ch_title"`
	EnTitle      string `json:"en_title" `
	Content      string `json:"content" `
	Path         string `json:"path" `
	Introduction string `json:"introduction" `
	Img          string `json:"img" `
	Sort         int    `json:"sort" `
}

type UpdateMessageIndexRequest struct {
	Id           int    `json:"id" `
	Project      string `json:"project" `
	Type         string `json:"type"  `
	Client       string `json:"client" `
	Year         int    `json:"year" `
	ChTitle      string `json:"ch_title"`
	EnTitle      string `json:"en_title" `
	Content      string `json:"content"`
	Path         string `json:"path"`
	Introduction string `json:"introduction"`
	Img          string `json:"img" `
	Sort         int    `json:"sort" `
}
type UpdateMessageIndexDetailRequest struct {
	Id            int           `json:"id" `
	DetailContent template.HTML `json:"detail_content" `
}

type saveMessageIndexResponse struct {
	Response
}

type DeleteMessageIndexRequest struct {
	Id int `form:"id" binding:"required"`
}

// MessageIndexListRequest 列表查询请求
type MessageIndexListRequest struct {
	Page     int    `form:"page" binding:"required,min=1"`     // 页码
	PageSize int    `form:"pageSize" binding:"required,min=1"` // 每页数量
	Project  string `form:"project"`                           // 项目筛选
	Type     string `form:"type"`                              // 类型筛选
}

// MessageIndexListResponse 列表查询响应
type MessageIndexListResponse struct {
	Total int64               `json:"total"` // 总记录数
	List  []*MessageIndexItem `json:"list"`  // 数据列表
}

// MessageIndexItem 列表项数据结构
type MessageIndexItem struct {
	ID            uint          `json:"id"`
	Project       string        `json:"project"`
	Type          string        `json:"type"`
	TypeName      string        `json:"type_name"`
	Client        string        `json:"client"`
	Year          int           `json:"year"`
	ChTitle       string        `json:"ch_title"`
	EnTitle       string        `json:"en_title"`
	Content       string        `json:"content"`
	Path          string        `json:"path"`
	Introduction  string        `json:"introduction"`
	Img           string        `json:"img"`
	Sort          int           `json:"sort"`
	DetailContent template.HTML `json:"detail_content"`
}

// MessageIndexDetailRequest 详情查询请求
type MessageIndexDetailRequest struct {
	ID uint `uri:"id" binding:"required,min=1"` // ID从URL路径中获取
}

// MessageIndexDetailResponse 详情查询响应
type MessageIndexDetailResponse struct {
	ID            uint          `json:"id"`
	Project       string        `json:"project"`
	Type          string        `json:"type"`
	Client        string        `json:"client"`
	Year          int           `json:"year"`
	ChTitle       string        `json:"ch_title" `
	EnTitle       string        `json:"en_title" `
	Content       string        `json:"content" `
	Path          string        `json:"path" `
	Introduction  string        `json:"introduction" `
	Img           string        `json:"img" `
	Sort          int           `json:"sort" `
	DetailContent template.HTML `json:"detail_content" `
}
