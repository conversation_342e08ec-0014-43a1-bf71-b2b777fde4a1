package v1

type SaveAwardRequest struct {
	Name string `json:"name" binding:"required"`
	Year int    `json:"year" binding:"required"`
	Work string `json:"work" binding:"required"`
}

type UpdateAwardRequest struct {
	ID   int    `json:"id" binding:"required"`
	Name string `json:"name" binding:"required"`
	Year int    `json:"year" binding:"required"`
	Work string `json:"work" binding:"required"`
}
type ListAwardRequest struct {
	Page     int `form:"page" binding:"required,min=1"`     // 页码
	PageSize int `form:"pageSize" binding:"required,min=1"` // 每页数量
}

type GetAwardRequest struct {
	ID int `form:"id" binding:"required"`
}

type DeleteAwardRequest struct {
	ID int `form:"id" binding:"required"`
}

type ListAwardResponse struct {
	Total int64        `json:"total"`
	List  []*AwardItem `json:"list"`
}
type AwardItem struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
	Year int    `json:"year"`
	Work string `json:"work"`
}
