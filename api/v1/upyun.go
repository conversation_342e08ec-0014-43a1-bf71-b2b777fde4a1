package v1

// UpyunSignatureRequest 又拍云签名请求
type UpyunSignatureRequest struct {
	SaveKey string `form:"save_key" binding:"omitempty"` // 保存路径，可选
}

// UpyunSignatureResponse 又拍云签名响应
type UpyunSignatureResponse struct {
	Policy    string `json:"policy"`    // 上传策略
	Signature string `json:"signature"` // 签名
	Bucket    string `json:"bucket"`    // 存储空间
	Domain    string `json:"domain"`    // 访问域名
	Expire    int64  `json:"expire"`    // 过期时间戳
}
