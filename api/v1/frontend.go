package v1

import "html/template"

type Image struct {
	URL string
	Alt string
}

type Index struct {
	BannerImages []Image
}

type Project struct {
	ID      string `json:"id"`
	Project string `json:"title"`
	Type    string `json:"type"`
	Client  string `json:"client"`
	Year    string `json:"year"`
	URL     string `json:"url"` // 对应 data-url 属性
}

type List struct {
	Items []Project `json:"items"`
}

type Work struct {
	ID    int    `json:"id"`
	Image string `json:"image"` // 图片路径
	Title string `json:"title"` // 作品标题（可从 alt 提取）
	Link  string `json:"link"`  // 详情页链接
}

// 作品列表
type WorkList struct {
	Works []Work `json:"works"`
}

type FollowUsItem struct {
	Name string `json:"name"`
	Link string `json:"link"`
	Sort int    `json:"sort"`
}

type About struct {
	EnTitle         string
	ChTitle         string
	Email           string
	Address         string
	FollowUs        []FollowUsItem `json:"follow_us"`
	MainURL         string
	TeamMembers     []TeamMember
	AwardCategories []AwardCategory
}

type TeamMember struct {
	Avatar string // 头像URL
	BioEn  string // 英文简介
	BioCn  string // 中文简介
}

type AwardCategory struct {
	Category string  // 分类名称（如 Brand Identity）
	Awards   []Award // 该分类下的奖项列表
}

type Award struct {
	Year string // 年份
	Work string // 奖项名称
}

type WorkDetail struct {
	ChTitle       string        // 中文标题
	EnTitle       string        // 英文标题
	Introduction  string        // 简介
	Content       string        // 内容描述
	DetailContent template.HTML // 详细内容
	Client        string        // 客户
	Year          int           // 年份
	Type          string        // 类型
	Photos        []string      // 作品图片列表
}
