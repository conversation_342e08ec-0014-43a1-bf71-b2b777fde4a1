// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/spf13/viper"
	"nunu-layout-admin/internal/handler"
	"nunu-layout-admin/internal/job"
	"nunu-layout-admin/internal/repository"
	"nunu-layout-admin/internal/server"
	"nunu-layout-admin/internal/service"
	"nunu-layout-admin/pkg/app"
	"nunu-layout-admin/pkg/jwt"
	"nunu-layout-admin/pkg/log"
	"nunu-layout-admin/pkg/server/http"
	"nunu-layout-admin/pkg/sid"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	jwtJWT := jwt.NewJwt(viperViper)
	db := repository.NewDB(viperViper, logger)
	syncedEnforcer := repository.NewCasbinEnforcer(viperViper, logger, db)
	handlerHandler := handler.NewHandler(logger)
	repositoryRepository := repository.NewRepository(logger, db, syncedEnforcer)
	transaction := repository.NewTransaction(repositoryRepository)
	sidSid := sid.NewSid()
	serviceService := service.NewService(transaction, logger, sidSid, jwtJWT)
	adminRepository := repository.NewAdminRepository(repositoryRepository)
	adminService := service.NewAdminService(serviceService, adminRepository)
	adminHandler := handler.NewAdminHandler(handlerHandler, adminService)
	messageIndexRepository := repository.NewMessageIndexRepository(repositoryRepository)
	messageIndexService := service.NewMessageIndexService(serviceService, messageIndexRepository)
	messageIndexHandler := handler.NewMessageIndexHandler(handlerHandler, messageIndexService)
	messageWorkRepository := repository.NewMessageWorkRepository(repositoryRepository)
	messageWorkService := service.NewMessageWorkService(serviceService, messageWorkRepository)
	messageWorkHandler := handler.NewMessageWorkHandler(handlerHandler, messageWorkService)
	userRepository := repository.NewUserRepository(repositoryRepository)
	userService := service.NewUserService(serviceService, userRepository)
	userHandler := handler.NewUserHandler(handlerHandler, userService)
	upyunService := service.NewUpyunService(serviceService, viperViper)
	upyunHandler := handler.NewUpyunHandler(handlerHandler, upyunService)
	categoryRepository := repository.NewCategoryRepository(repositoryRepository)
	categoryService := service.NewCategoryService(serviceService, categoryRepository)
	categoryHandler := handler.NewCategoryHandler(handlerHandler, categoryService)
	memberRepository := repository.NewMemberRepository(repositoryRepository)
	memberService := service.NewMemberService(serviceService, memberRepository)
	memberHandler := handler.NewMemberHandler(handlerHandler, memberService)
	aboutRepository := repository.NewAboutRepository(repositoryRepository)
	aboutService := service.NewAboutService(serviceService, aboutRepository)
	aboutHandler := handler.NewAboutHandler(handlerHandler, aboutService)
	messageIndexDetailRepository := repository.NewMessageIndexDetailRepository(repositoryRepository)
	awardRepository := repository.NewAwardRepository(repositoryRepository)
	frontendHandler := handler.NewFrontendHandler(handlerHandler, categoryRepository, memberRepository, aboutRepository, messageWorkRepository, messageIndexRepository, messageIndexDetailRepository, awardRepository)
	messageIndexDetailService := service.NewMessageIndexDetailService(serviceService, messageIndexDetailRepository)
	messageIndexDetailHandler := handler.NewMessageIndexDetailHandler(handlerHandler, messageIndexDetailService)
	awardService := service.NewAwardService(serviceService, awardRepository)
	awardHandler := handler.NewAwardHandler(handlerHandler, awardService)
	httpServer := server.NewHTTPServer(logger, viperViper, jwtJWT, syncedEnforcer, adminHandler, messageIndexHandler, messageWorkHandler, userHandler, upyunHandler, categoryHandler, memberHandler, aboutHandler, frontendHandler, messageIndexDetailHandler, awardHandler)
	jobJob := job.NewJob(transaction, logger, sidSid)
	userJob := job.NewUserJob(jobJob, userRepository)
	jobServer := server.NewJobServer(logger, userJob)
	appApp := newApp(httpServer, jobServer)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRepository, repository.NewTransaction, repository.NewUserRepository, repository.NewCasbinEnforcer, repository.NewAdminRepository, repository.NewMessageIndexRepository, repository.NewMessageWorkRepository, repository.NewCategoryRepository, repository.NewMemberRepository, repository.NewAboutRepository, repository.NewMessageIndexDetailRepository, repository.NewAwardRepository)

var serviceSet = wire.NewSet(service.NewService, service.NewUserService, service.NewAdminService, service.NewMessageIndexService, service.NewMessageWorkService, service.NewUpyunService, service.NewCategoryService, service.NewMemberService, service.NewAboutService, service.NewMessageIndexDetailService, service.NewAwardService)

var handlerSet = wire.NewSet(handler.NewHandler, handler.NewUserHandler, handler.NewAdminHandler, handler.NewMessageIndexHandler, handler.NewMessageWorkHandler, handler.NewUpyunHandler, handler.NewCategoryHandler, handler.NewMemberHandler, handler.NewAboutHandler, handler.NewFrontendHandler, handler.NewMessageIndexDetailHandler, handler.NewAwardHandler)

var jobSet = wire.NewSet(job.NewJob, job.NewUserJob)

var serverSet = wire.NewSet(server.NewHTTPServer, server.NewJobServer)

// build App
func newApp(
	httpServer *http.Server,
	jobServer *server.JobServer,

) *app.App {
	return app.NewApp(app.WithServer(httpServer, jobServer), app.WithName("demo-server"))
}
