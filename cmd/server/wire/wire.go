//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/spf13/viper"
	"nunu-layout-admin/internal/handler"
	"nunu-layout-admin/internal/job"
	"nunu-layout-admin/internal/repository"
	"nunu-layout-admin/internal/server"
	"nunu-layout-admin/internal/service"
	"nunu-layout-admin/pkg/app"
	"nunu-layout-admin/pkg/jwt"
	"nunu-layout-admin/pkg/log"
	"nunu-layout-admin/pkg/server/http"
	"nunu-layout-admin/pkg/sid"
)

var repositorySet = wire.NewSet(
	repository.NewDB,
	//repository.NewRedis,
	repository.NewRepository,
	repository.NewTransaction,
	repository.NewUserRepository,
	repository.NewCasbinEnforcer,
	repository.NewAdminRepository,
	repository.NewMessageIndexRepository,
	repository.NewMessageWorkRepository,
	repository.NewCategoryRepository,
	repository.NewMemberRepository,
	repository.NewAboutRepository,
	repository.NewMessageIndexDetailRepository,
	repository.NewAwardRepository,
)

var serviceSet = wire.NewSet(
	service.NewService,
	service.NewUserService,
	service.NewAdminService,
	service.NewMessageIndexService,
	service.NewMessageWorkService,
	service.NewUpyunService,
	service.NewCategoryService,
	service.NewMemberService,
	service.NewAboutService,
	service.NewMessageIndexDetailService,
	service.NewAwardService,
)

var handlerSet = wire.NewSet(
	handler.NewHandler,
	handler.NewUserHandler,
	handler.NewAdminHandler,
	handler.NewMessageIndexHandler,
	handler.NewMessageWorkHandler,
	handler.NewUpyunHandler,
	handler.NewCategoryHandler,
	handler.NewMemberHandler,
	handler.NewAboutHandler,
	handler.NewFrontendHandler,
	handler.NewMessageIndexDetailHandler,
	handler.NewAwardHandler,
)

var jobSet = wire.NewSet(
	job.NewJob,
	job.NewUserJob,
)
var serverSet = wire.NewSet(
	server.NewHTTPServer,
	server.NewJobServer,
)

// build App
func newApp(
	httpServer *http.Server,
	jobServer *server.JobServer,
	// task *server.Task,
) *app.App {
	return app.NewApp(
		app.WithServer(httpServer, jobServer),
		app.WithName("demo-server"),
	)
}

func NewWire(*viper.Viper, *log.Logger) (*app.App, func(), error) {
	panic(wire.Build(
		repositorySet,
		serviceSet,
		handlerSet,
		jobSet,
		serverSet,
		sid.NewSid,
		jwt.NewJwt,
		newApp,
	))
}
