package handler

import (
	"net/http"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/service"

	"github.com/gin-gonic/gin"
)

type MemberHandler struct {
	*Handler
	memberService service.MemberService
}

func NewMemberHandler(
	handler *Handler,
	memberService service.MemberService,
) *MemberHandler {
	return &MemberHandler{
		Handler:       handler,
		memberService: memberService,
	}
}

func (h *MemberHandler) CreateMember(ctx *gin.Context) {
	var req v1.SaveMemberRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.memberService.CreateMember(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)

}

func (h *MemberHandler) UpdateMember(ctx *gin.Context) {
	var req v1.UpdateMemberRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.memberService.UpdateMember(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}
func (h *MemberHandler) DeleteMember(ctx *gin.Context) {
	var req v1.DeleteMemberRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	// 调用服务层删除记录
	err := h.memberService.DeleteMember(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err.Error())
		return
	}

	v1.HandleSuccess(ctx, nil)
}

func (h *MemberHandler) ListMember(ctx *gin.Context) {
	var req v1.ListMemberRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	// 调用服务层获取列表
	list, total, err := h.memberService.ListMember(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	// 构建响应
	resp := &v1.ListMemberResponse{
		Total: total,
		List:  make([]*v1.MemberItem, len(list)),
	}

	// 转换模型为API响应格式
	for i, item := range list {
		resp.List[i] = &v1.MemberItem{
			ID:     item.ID,
			Avatar: item.Avatar,
			BioEn:  item.BioEn,
			BioCn:  item.BioCn,
		}
	}

	v1.HandleSuccess(ctx, resp)
}
