package handler

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/service"
)

type MessageWorkHandler struct {
	*Handler
	messageWorkService service.MessageWorkService
}

func NewMessageWorkHandler(
	handler *Handler,
	messageWorkService service.MessageWorkService,
) *MessageWorkHandler {
	return &MessageWorkHandler{
		Handler:            handler,
		messageWorkService: messageWorkService,
	}
}

func (h *MessageWorkHandler) Create(ctx *gin.Context) {
	var req v1.CreateMessageWorkRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	// 调用服务层创建记录
	err := h.messageWorkService.Create(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err.Error())
		return
	}

	v1.HandleSuccess(ctx, nil)
}

func (h *MessageWorkHandler) Update(ctx *gin.Context) {
	var req v1.UpdateMessageWorkRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.messageWorkService.Update(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

func (h *MessageWorkHandler) Delete(ctx *gin.Context) {
	var req v1.DeleteMessageWorkRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	// 调用服务层删除记录
	err := h.messageWorkService.Delete(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err.Error())
		return
	}

	v1.HandleSuccess(ctx, nil)
}

func (h *MessageWorkHandler) List(ctx *gin.Context) {
	var req v1.ListMessageWorkRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	// 调用服务层获取列表
	list, total, err := h.messageWorkService.List(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	// 构建响应
	resp := &v1.ListMessageWorkResponse{
		Total: total,
		List:  make([]*v1.MessageWorkItem, len(list)),
	}

	// 转换模型为API响应格式
	for i, item := range list {
		resp.List[i] = &v1.MessageWorkItem{
			Id:   int(item.ID),
			Sort: item.Sort,
			Url:  item.Url,
		}
	}

	v1.HandleSuccess(ctx, resp)
}

func (h *MessageWorkHandler) Detail(ctx *gin.Context) {
	var req v1.MessageWorkDetailRequest
	if err := ctx.ShouldBindUri(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	// 调用服务层获取详情
	item, err := h.messageWorkService.Detail(ctx, req.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			v1.HandleError(ctx, http.StatusNotFound, v1.ErrNotFound, nil)
		} else {
			v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		}
		return
	}

	// 构建响应
	resp := &v1.MessageWorkDetailResponse{
		ID:   item.ID,
		Url:  item.Url,
		Sort: item.Sort,
	}

	v1.HandleSuccess(ctx, resp)
}
