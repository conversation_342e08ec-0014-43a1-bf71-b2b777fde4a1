package handler

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/service"
)

type CategoryHandler struct {
	*Handler
	categoryService service.CategoryService
}

func NewCategoryHandler(
	handler *Handler,
	categoryService service.CategoryService,
) *CategoryHandler {
	return &CategoryHandler{
		Handler:         handler,
		categoryService: categoryService,
	}
}

func (h *CategoryHandler) CreateCategory(ctx *gin.Context) {
	var req v1.SaveCategoryRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.categoryService.CreateCategory(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)

}

func (h *CategoryHandler) UpdateCategory(ctx *gin.Context) {
	var req v1.UpdateCategoryRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.categoryService.UpdateCategory(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

func (h *CategoryHandler) GetCategory(ctx *gin.Context) {
	var req v1.GetCategoryRequest
	if err := ctx.ShouldBindUri(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	// 调用服务层获取详情
	item, err := h.categoryService.GetCategory(ctx, req.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			v1.HandleError(ctx, http.StatusNotFound, v1.ErrNotFound, nil)
		} else {
			v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		}
		return
	}

	//// 构建响应
	//resp := &v1.MessageIndexDetailResponse{
	//	ID:      item.ID,
	//	Project: item.Project,
	//	Type:    item.Type,
	//	Client:  item.Client,
	//	Year:    item.Year,
	//}

	v1.HandleSuccess(ctx, item)
}

func (h *CategoryHandler) ListCategory(ctx *gin.Context) {
	var req v1.ListCategoryRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	// 调用服务层获取列表
	list, total, err := h.categoryService.ListCategory(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	// 构建响应
	resp := &v1.ListCategoryResponse{
		Total: total,
		List:  make([]*v1.CategoryItem, len(list)),
	}

	// 转换模型为API响应格式
	for i, item := range list {
		resp.List[i] = &v1.CategoryItem{
			TypeName: item.TypeName,
			ID:       item.ID,
			Sort:     item.Sort,
		}
	}

	v1.HandleSuccess(ctx, resp)
}

func (h *CategoryHandler) DeleteCategory(ctx *gin.Context) {
	var req v1.DeleteCategoryRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	// 调用服务层删除记录
	err := h.categoryService.DeleteCategory(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err.Error())
		return
	}

	v1.HandleSuccess(ctx, nil)
}
