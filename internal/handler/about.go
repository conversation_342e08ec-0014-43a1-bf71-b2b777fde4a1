package handler

import (
	"errors"
	"net/http"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type AboutHandler struct {
	*Handler
	aboutService service.AboutService
}

func NewAboutHandler(
	handler *Handler,
	aboutService service.AboutService,
) *AboutHandler {
	return &AboutHandler{
		Handler:      handler,
		aboutService: aboutService,
	}
}

func (h *AboutHandler) GetAbout(ctx *gin.Context) {
	//var req v1.GetAboutRequest
	//
	//if err := ctx.ShouldBindUri(&req); err != nil {
	//	v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
	//	return
	//}

	// 调用服务层获取详情
	item, err := h.aboutService.GetAbout(ctx)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			v1.HandleError(ctx, http.StatusNotFound, v1.ErrNotFound, nil)
		} else {
			v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		}
		return

	}

	resp := &v1.GetAboutResponse{
		ID:      item.ID,
		Img:     item.Img,
		ChTitle: item.ChTitle,
		EnTitle: item.EnTitle,
		Email:   item.Email,
		Address: item.Address,
	}
	for _, item := range item.FollowUs {
		resp.FollowUs = append(resp.FollowUs, v1.FollowUsItem{
			Name: item.Name,
			Link: item.Link,
			Sort: item.Sort,
		})
	}
	v1.HandleSuccess(ctx, resp)
}

func (h *AboutHandler) CreateAbout(ctx *gin.Context) {
	var req v1.SaveAboutRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.aboutService.CreateAbout(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)

}

func (h *AboutHandler) UpdateAbout(ctx *gin.Context) {
	var req v1.UpdateAboutRequest
	if err := ctx.ShouldBind(&req); err != nil {
		h.logger.Error("ShouldBind err: %v", zap.Error(err))
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.aboutService.UpdateAbout(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}
