package handler

import (
	"github.com/gin-gonic/gin"
	"net/http"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/service"
)

type UpyunHandler struct {
	*Handler
	upyunService service.UpyunService
}

func NewUpyunHandler(
	handler *Handler,
	upyunService service.UpyunService,
) *UpyunHandler {
	return &UpyunHandler{
		Handler:      handler,
		upyunService: upyunService,
	}
}

// GenerateSignature 生成又拍云上传签名
func (h *UpyunHandler) GenerateSignature(ctx *gin.Context) {
	var req v1.UpyunSignatureRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	// 默认保存路径，可根据需求修改
	saveKey := req.SaveKey
	if saveKey == "" {
		saveKey = "/uploads/${filename}"
	}

	// 调用服务层生成签名
	signature, err := h.upyunService.GenerateSignature(ctx, saveKey)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err.Error())
		return
	}

	v1.HandleSuccess(ctx, signature)
}
