package handler

import (
	"errors"
	"net/http"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AwardHandler struct {
	*Handler
	awardService service.AwardService
}

func NewAwardHandler(
	handler *Handler,
	awardService service.AwardService,
) *AwardHandler {
	return &AwardHandler{
		Handler:      handler,
		awardService: awardService,
	}
}

func (h *AwardHandler) CreateAward(ctx *gin.Context) {
	var req v1.SaveAwardRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.awardService.CreateAward(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

func (h *AwardHandler) UpdateAward(ctx *gin.Context) {
	var req v1.UpdateAwardRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.awardService.UpdateAward(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

func (h *AwardHandler) GetAward(ctx *gin.Context) {
	var req v1.GetAwardRequest
	if err := ctx.ShouldBindUri(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	item, err := h.awardService.GetAward(ctx, int64(req.ID))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			v1.HandleError(ctx, http.StatusNotFound, v1.ErrNotFound, nil)
		} else {
			v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		}
		return
	}

	v1.HandleSuccess(ctx, item)
}

func (h *AwardHandler) ListAward(ctx *gin.Context) {
	var req v1.ListAwardRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	list, total, err := h.awardService.ListAward(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	resp := &v1.ListAwardResponse{
		Total: total,
		List:  make([]*v1.AwardItem, len(list)),
	}

	for i, item := range list {
		resp.List[i] = &v1.AwardItem{
			ID:   int(item.ID),
			Name: item.Name,
			Year: item.Year,
			Work: item.Work,
		}
	}

	v1.HandleSuccess(ctx, resp)
}

func (h *AwardHandler) DeleteAward(ctx *gin.Context) {
	var req v1.DeleteAwardRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	err := h.awardService.DeleteAward(ctx, &req)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, err.Error())
		return
	}

	v1.HandleSuccess(ctx, nil)
}
