package handler

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/service"
)

type MessageIndexDetailHandler struct {
	*Handler
	messageIndexDetailService service.MessageIndexDetailService
}

func NewMessageIndexDetailHandler(
	handler *Handler,
	messageIndexDetailService service.MessageIndexDetailService,
) *MessageIndexDetailHandler {
	return &MessageIndexDetailHandler{
		Handler:                   handler,
		messageIndexDetailService: messageIndexDetailService,
	}
}

func (h *MessageIndexDetailHandler) GetMessageIndexDetail(ctx *gin.Context) {
	var req v1.GetDetailRequest
	if err := ctx.ShouldBindUri(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err.Error())
		return
	}

	// 调用服务层获取详情
	item, err := h.messageIndexDetailService.GetMessageIndexDetail(ctx, req.IndexId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			v1.HandleError(ctx, http.StatusNotFound, v1.ErrNotFound, nil)
		} else {
			v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		}
		return
	}

	v1.HandleSuccess(ctx, item)

}

func (h *MessageIndexDetailHandler) CreateOrUpdateMessageIndexDetail(ctx *gin.Context) {
	var req v1.CreateOrUpdateDetailRequest
	if err := ctx.ShouldBind(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.messageIndexDetailService.CreateOrUpdateMessageIndexDetail(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}
