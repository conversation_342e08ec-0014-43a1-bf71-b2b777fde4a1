package handler

import (
	"fmt"
	"net/http"
	v1 "nunu-layout-admin/api/v1"
	"sort"
	"strconv"

	"nunu-layout-admin/internal/repository"

	"github.com/gin-gonic/gin"
)

// -----------------

type FrontendHandler struct {
	*Handler
	categoryRepository           repository.CategoryRepository
	memberRepository             repository.MemberRepository
	aboutRepository              repository.AboutRepository
	messageWorkRepository        repository.MessageWorkRepository
	messageIndexRepository       repository.MessageIndexRepository
	messageIndexDetailRepository repository.MessageIndexDetailRepository
	awardRepository              repository.AwardRepository
}

func NewFrontendHandler(
	handler *Handler,
	categoryRepository repository.CategoryRepository,
	memberRepository repository.MemberRepository,
	aboutRepository repository.AboutRepository,
	messageWorkRepository repository.MessageWorkRepository,
	messageIndexRepository repository.MessageIndexRepository,
	messageIndexDetailRepository repository.MessageIndexDetailRepository,
	awardRepository repository.AwardRepository,
) *FrontendHandler {
	return &FrontendHandler{
		Handler:                      handler,
		categoryRepository:           categoryRepository,
		memberRepository:             memberRepository,
		aboutRepository:              aboutRepository,
		messageWorkRepository:        messageWorkRepository,
		messageIndexRepository:       messageIndexRepository,
		messageIndexDetailRepository: messageIndexDetailRepository,
		awardRepository:              awardRepository,
	}
}

// 已完成
func (h *FrontendHandler) Index(ctx *gin.Context) {

	indexs, _, err := h.messageWorkRepository.List(ctx, &v1.ListMessageWorkRequest{})
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 准备数据
	// 将数据库中的works转换为banner图片
	data := v1.Index{
		BannerImages: make([]v1.Image, 0, len(indexs)),
	}

	for i, work := range indexs {
		data.BannerImages = append(data.BannerImages, v1.Image{
			URL: work.Url,
			Alt: fmt.Sprintf("作品%d", i+1),
		})
	}

	// 渲染模板
	ctx.HTML(200, "index.html", data)
}

// 已完成
func (h *FrontendHandler) List(ctx *gin.Context) {

	lists, err := h.messageIndexRepository.List(ctx, &v1.MessageIndexListRequest{})
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 准备数据
	data := v1.List{
		Items: make([]v1.Project, 0, lists.Total),
	}

	// 将数据库中的数据转换为前端需要的格式
	for no, item := range lists.List {
		data.Items = append(data.Items, v1.Project{
			ID:      fmt.Sprintf("%d", no+1),
			Project: item.Project,
			Type:    item.Type,
			Client:  item.Client,
			Year:    fmt.Sprintf("%d", item.Year),
			URL:     fmt.Sprintf("./work_detail/%d", item.ID),
		})
	}

	// 渲染模板
	ctx.HTML(200, "list.html", data)
}

// 已完成
func (h *FrontendHandler) Works(ctx *gin.Context) {
	works, err := h.messageIndexRepository.List(ctx, &v1.MessageIndexListRequest{})
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 准备数据
	data := v1.WorkList{
		Works: make([]v1.Work, 0, len(works.List)),
	}

	for _, work := range works.List {
		data.Works = append(data.Works, v1.Work{
			ID:    int(work.ID),
			Image: work.Img,
			Title: fmt.Sprintf("Work Title %d", work.ID),
			Link:  fmt.Sprintf("work_detail/%d", work.ID),
		})
	}

	// 渲染模板
	ctx.HTML(200, "works.html", data)
}

func (h *FrontendHandler) About(ctx *gin.Context) {

	about, err := h.aboutRepository.GetAbout(ctx)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	members, _, err := h.memberRepository.ListMember(ctx, &v1.ListMemberRequest{})
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	awards, _, err := h.awardRepository.ListAward(ctx, &v1.ListAwardRequest{})
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 准备数据
	data := v1.About{}
	data.EnTitle = about.EnTitle
	data.ChTitle = about.ChTitle
	data.Email = about.Email
	data.Address = about.Address
	// 将数据库中的FollowUsItems转换为前端需要的格式
	data.FollowUs = make([]v1.FollowUsItem, 0, len(about.FollowUs))
	for _, item := range about.FollowUs {
		data.FollowUs = append(data.FollowUs, v1.FollowUsItem{
			Name: item.Name,
			Link: item.Link,
			Sort: item.Sort,
		})
	}
	fmt.Println("data.FollowUs: ", data.FollowUs)

	// 按sort字段升序排序
	sort.Slice(data.FollowUs, func(i, j int) bool {
		return data.FollowUs[i].Sort < data.FollowUs[j].Sort
	})

	data.MainURL = about.Img

	data.AwardCategories = make([]v1.AwardCategory, 0)

	awardsMap := make(map[string][]v1.Award)
	for _, award := range awards {
		awardsMap[award.Name] = append(awardsMap[award.Name], v1.Award{
			Year: fmt.Sprintf("%d", award.Year),
			Work: award.Work,
		})
	}

	for _, category := range []string{"award", "interview", "exhibition"} {
		awards := awardsMap[category]
		data.AwardCategories = append(data.AwardCategories, v1.AwardCategory{
			Category: category,
			Awards:   awards,
		})
	}

	data.TeamMembers = make([]v1.TeamMember, 0, len(members))
	for _, member := range members {
		data.TeamMembers = append(data.TeamMembers, v1.TeamMember{
			Avatar: member.Avatar,
			BioEn:  member.BioEn,
			BioCn:  member.BioCn,
		})
	}

	// 渲染模板
	ctx.HTML(200, "about.html", data)
}

func (h *FrontendHandler) WorkDetail(ctx *gin.Context) {

	idParam := ctx.Param("id")
	if idParam == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "id is required"})
		return
	}

	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "id is not a valid integer"})
		return
	}

	indexDetail, err := h.messageIndexRepository.Detail(ctx, uint(id))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// indexCategory, err := h.categoryRepository.GetCategory(ctx, indexDetail.Type)
	// if err != nil {
	// 	ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
	// 	return
	// }

	messageIndexDetailList, err := h.messageIndexDetailRepository.GetMessageIndexDetail(ctx, int(indexDetail.ID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	data := v1.WorkDetail{}
	data.ChTitle = indexDetail.ChTitle
	data.EnTitle = indexDetail.EnTitle
	data.Introduction = indexDetail.Introduction
	data.Content = indexDetail.Content
	data.DetailContent = indexDetail.DetailContent
	data.Client = indexDetail.Client
	data.Year = indexDetail.Year
	data.Type = indexDetail.Type
	data.Photos = []string{}
	for _, item := range messageIndexDetailList.ImgArr {
		data.Photos = append(data.Photos, item.Img)
	}

	// 渲染模板
	ctx.HTML(200, "work_detail.html", data)
}
