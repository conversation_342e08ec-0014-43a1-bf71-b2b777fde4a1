<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>A ONE AND A TWO DESIGN</title>
  <script src="//static.aoneandatwodesign.com/static/style/tailwind.js"></script>
  <link href="//static.aoneandatwodesign.com/static/style/all.min.css" rel="stylesheet">

  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .global-selection {
        @apply selection:bg-black selection:text-white;
      }
      .text-cn {
        transform: scaleY(0.95);
        transform-origin: center;
        display: inline-block;
      }
      .mobile-padding {
        @apply px-[clamp(1rem,3vw,3.5rem)];
      }
      .adaptive-spacing {
        @apply ml-[clamp(10px,3vw,42px)];
      }
      .mobile-divider {
        @apply md:hidden  border-gray-200 my-[clamp(2rem,5vw,3rem)];
      }
    }
    body {
      @apply global-selection font-light;
    }

    /* 导入自定义字体 */
    @font-face {
      font-family: "BradfordLL-Book";
      src: url("//static.aoneandatwodesign.com/static/fonts/BradfordLL-Book.otf") format("opentype");
    }

    @font-face {
      font-family: "SourceHanSerifCN-Regular";
      src: url("//static.aoneandatwodesign.com/static/fonts/SourceHanSerifCN-Regular.otf") format("opentype");
    }

  </style>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            // 设置字体优先级
            mixed: ['BradfordLL-Book', 'SourceHanSerifCN-Regular', 'serif'],
          },
          // 添加选中文本的样式
          textSelection: {
            'selected': {
              backgroundColor: '#000',
              color: '#fff'
            }
          }
        }
      }
    }
  </script>

</head>

<body class="bg-white text-black font-mixed font-light ">
  <!-- 导航栏 -->
  <nav class="w-full transition-all duration-300 antialiased" id="main-nav">
    <div class="flex justify-between items-center text-[clamp(1.5rem,3vw,2rem)] leading-[clamp(1.75rem,3vw,1.75rem)] mt-[clamp(2.8125rem,5vw,2.8125rem)] mobile-padding">
      <div class="flex-shrink-0">
        <a href="index.html">
          <span style="letter-spacing: -0.05em">a</span
          ><span style="letter-spacing: -0.03em">O</span
          ><span style="letter-spacing: -0.04em">n</span
          ><span style="letter-spacing: -0.03em">e</span
          ><span style="letter-spacing: -0.03em">a</span
          ><span style="letter-spacing: -0.04em">n</span
          ><span style="letter-spacing: -0.03em">d</span
          ><span style="letter-spacing: -0.122em">a</span
          ><span style="letter-spacing: -0.14em">T</span
          ><span style="letter-spacing: -0.04em">w</span>o
        </a>
      </div>
      <!-- 桌面导航 -->
      <div class="hidden md:flex" >
        <a href="/works" class="mr-[clamp(2.4375rem,3vw,2.4375rem)]">work</a>
        <a href="/list" class="mr-[clamp(2.125rem,3vw,2.125rem)]">index</a>
        <a href="/about" class="">about</a>
      </div>
      <!-- 移动端菜单按钮 -->
      <button id="menu-toggle" class="md:hidden text-2xl">
        <i class="fa fa-bars"></i>
      </button>
    </div>
    <!-- 移动端导航菜单 -->
    <div id="mobile-menu" class="hidden md:hidden bg-white absolute w-full z-10 shadow-lg mobile-padding">
      <div class="flex flex-col py-4">
        <a href="/works" class="py-2">work</a>
        <a href="/about" class="py-2">about</a>
      </div>
    </div>
  </nav>

  <!-- 作品展示区域 -->
  <main class="mobile-padding mt-[clamp(4.375rem,5vw,4.375rem)]">
    {{if .MainURL}}
    <div class="w-full h-[clamp(23.125rem,30vw,23.125rem)] mt-[clamp(4.375rem,5vw,4.375rem)] mb-[clamp(4.875rem,5vw,4.875rem)]">
      {{if hasSuffix .MainURL ".mp4"}}
        <video src="{{.MainURL}}" class="w-full h-full object-cover" autoplay loop muted playsinline>
          <source src="{{.MainURL}}" type="video/mp4">
        </video>
      {{else}}
        <img src="{{.MainURL}}" alt="关于我们的横幅图片" class="w-full h-full object-cover">
      {{end}}
    </div>
    {{end}}
    
    <div class="flex flex-col lg:flex-row gap-[clamp(2rem,7.812vw,140px)]">
      <!-- 左侧公司信息 -->
      <div class="w-full lg:w-[clamp(37.0625rem,35vw,35vw)] mb-[clamp(4rem,5vw,4rem)] ">
        {{if .EnTitle}}
        <p class="text-[24px] leading-[30px] tracking-[1.6%]  antialiased">
          {{ nl2br .EnTitle}}
        </p>
        {{end}}
        {{if .ChTitle}}
        <p class="text-[22px] leading-[39px] tracking-[0.2px] mt-[clamp(2.5rem,5vw,2.5rem)]  text-cn ">
          {{ nl2br .ChTitle}}
        </p>
        {{end}}

        <div class="hidden md:block">
        <p class="mt-[clamp(4.5rem,5vw,4.5rem)]">
          <span class="text-[24px] leading-[36px] tracking-[-0.24px] antialiased">Contact</span> 
        </p>
        <p class="text-[24px] leading-[29px] tracking-[-0.24px] mt-[clamp(1rem,2vw,1rem)] antialiased">
          {{if .Email}}
          Email: {{ nl2br .Email}}
          <br>
          {{end}}

          {{if .Address}}
          Address: {{ nl2br .Address}}
          <br><br>
          {{end}}
          
          {{if .FollowUs}}
          Follow Us
          <br>
          {{range .FollowUs}}
          <a href="{{.Link}}" target="_blank" class="mr-[14px]">{{nl2br .Name}}</a>
          {{end}}
          <br>
          {{end}}

        </p>
      </div>
      </div>

      <!-- 移动端分割线 -->
      <div class="mobile-divider"></div>

      <!-- 中间作品列表 -->
      <div class="w-full lg:w-[clamp(28.125rem,25vw,25vw)] mb-[clamp(4rem,5vw,4rem)] antialiased">
        {{ range .AwardCategories }}
        <div class="mb-[clamp(3.5rem,5vw,3.5rem)]">
          <div class="text-[18px] leading-[22px] mb-[22px]">
            {{ nl2br .Category }}  <!-- 分类名称 -->
          </div>
          <div class="">
            {{ range .Awards }}  <!-- 遍历该分类下的奖项 -->
            <div class="flex">
              <span class="text-[18px] leading-[22px] mr-[14px]">
                {{ nl2br .Year }}  <!-- 年份 -->
              </span>
              <span class="text-[18px] leading-[22px]">
                {{ nl2br .Work }}  <!-- 奖项名称 -->
              </span>
            </div>
            {{ end }}
          </div>
        </div>
        {{ end }}

      </div>

      <!-- 移动端分割线 -->
      <div class="mobile-divider"></div>

      <!-- 右侧团队信息 -->

      <div class="w-full lg:w-[clamp(31rem,30vw,30vw)]">
        <div class="text-[18px] pl-[90px] ml-[clamp(1.5rem,3vw,2.625rem)] leading-[22px]  mb-[clamp(1.3125rem,2vw,1.3125rem)]">Member</div>
        
        <div class="">
            {{ range $index, $member := .TeamMembers }}
            <div class="{{ if ne $index 0 }}mt-[clamp(3.5625rem,5vw,3.5625rem)]{{ end }}">
                <div class="flex mobile-column">

                    {{if $member.Avatar}}
                        {{if hasSuffix $member.Avatar ".mp4"}}
                            <video src="{{$member.Avatar}}" 
                                   class="w-[clamp(5.625rem,5vw,5.625rem)] h-[clamp(5.625rem,5vw,5.625rem)] object-cover" 
                                   autoplay loop muted playsinline>
                                <source src="{{$member.Avatar}}" type="video/mp4">
                            </video>
                        {{else}}
                            <img src="{{$member.Avatar}}" 
                                 alt="头像" 
                                 class="w-[clamp(5.625rem,5vw,5.625rem)] h-[clamp(5.625rem,5vw,5.625rem)] object-cover">
                        {{end}}
                    {{else}}
                        <!-- 没有头像时显示占位块 -->
                            <img src="//static.aoneandatwodesign.com/static/white.jpg" 
                            alt="头像" 
                            class="w-[clamp(5.625rem,5vw,5.625rem)] h-[clamp(5.625rem,5vw,5.625rem)] object-cover">
                    {{end}}

                    <div class="adaptive-spacing">
                        {{if $member.BioEn}}
                        <p class="text-[18px] leading-[22px] antialiased">
                            {{ nl2br $member.BioEn }}
                        </p>
                        {{end}}
                        
                        {{if $member.BioCn}}
                        <p class="text-[14px] leading-[23px] mt-[clamp(2.5rem,5vw,2.5rem)] text-cn ">
                            {{ nl2br $member.BioCn }}
                        </p>
                        {{end}}
                    </div>
                </div>
            </div>
            {{ end }}
        </div>
    </div>

      <div class="mobile-divider"></div>

      <div class=" md:hidden">
        <p class=" text-[24px] mt-[clamp(4.5rem,5vw,4.5rem)]">
          <span class="text-[24px] leading-[36px] tracking-[-0.24px] antialiased">Contact</span> 
        </p>
        <p class="text-[24px] leading-[28px] tracking-[-0.24px] mt-[clamp(1rem,2vw,1rem)] antialiased">

          {{if .Email}}
          Email: {{ nl2br .Email}}
            <br>
          {{end}}

          {{if .Address}}
          Address: {{ nl2br .Address}}
            <br><br>
          {{end}}

          {{if .FollowUs}}
          Follow Us
          <br>
          {{range .FollowUs}}
          <a href="{{.Link}}" target="_blank" class="">{{nl2br .Name}}</a>
          {{end}}
          <br>
          {{end}}

        </p>
      </div>
    </div>
  </main>

  <!-- 页脚 -->
  <footer class="pt-[clamp(200px,15vw,420px)] pb-[clamp(200px,15vw,420px)] mobile-padding">
    <div class="mx-auto">
      <p class="text-center text-[clamp(0.875rem,1vw,0.875rem)] leading-[clamp(1.25rem,1vw,1.25rem)] antialiased">
        © 2019-2025 a one and a two design. All rights reserved
      </p>
    </div>
  </footer>
  
  <!-- 回到顶部按钮 -->
  <button onclick="scrollToTop()" id="backToTop" class="fixed bottom-[clamp(1.5rem,2vw,56px)] right-[clamp(1.5rem,2vw,56px)] w-[clamp(3rem,5vw,3.5rem)] h-[clamp(3rem,5vw,3.5rem)] flex items-center justify-center   mix-blend-exclusion">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="240 380 50 80" class="w-[clamp(3rem,5vw,3.5rem)] h-[clamp(3rem,5vw,3.5rem)]">
      <path d="M263.79,389.4l14.51,15.38h-8.76v24.72h-11.51v-24.72h-8.76L263.79,389.4z M263.79,386.93l-18.48,19.55h11.03v24.72h14.9v-24.72h11.03L263.79,386.93z" fill="white" />
    </svg>
  </button>

  <script>
    // 移动端菜单切换
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    
    menuToggle.addEventListener('click', () => {
      mobileMenu.classList.toggle('hidden');
    });

    // 控制回到顶部按钮的显示和隐藏
    window.onscroll = function() {
      const backToTopButton = document.getElementById('backToTop');
      if (document.body.scrollTop > 200 || document.documentElement.scrollTop > 200) {
        backToTopButton.classList.remove('opacity-0', 'invisible');
        backToTopButton.classList.add('opacity-100', 'visible');
      } else {
        backToTopButton.classList.remove('opacity-100', 'visible');
        backToTopButton.classList.add('opacity-0', 'invisible');
      }
    };

    // 平滑滚动到顶部
    function scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  </script>
</body>
</html>
