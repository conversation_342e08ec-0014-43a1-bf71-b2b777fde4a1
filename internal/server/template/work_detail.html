<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>A ONE AND A TWO DESIGN</title>
    <script src="//static.aoneandatwodesign.com/static/style/tailwind.js"></script>
    <link
      rel="stylesheet"
      href="//static.aoneandatwodesign.com/static/style/all.min.css"
    />

    <style type="text/tailwindcss">
      @layer utilities {
        .content-auto {
          content-visibility: auto;
        }
        .global-selection {
          @apply selection:bg-black selection:text-white;
        }
      .mobile-padding {
        @apply px-[clamp(24px,3vw,3.5rem)];
      }
      .mobile-column {
        @apply flex-col md:flex-row;
      }
      .adaptive-spacing {
        @apply mt-[clamp(1rem,2vw,1.5rem)] md:mt-0 md:ml-[clamp(1.5rem,3vw,2.625rem)];
      }
      .column-spacing {
        @apply lg:pr-[clamp(2rem,4vw,5rem)];
      }
      .mobile-divider {
        @apply md:hidden border-t border-gray-200 my-[clamp(2rem,5vw,3rem)];
      }
      }
      body {
        @apply global-selection;
      }

      .text-cn {
        transform: scaleY(0.95);
        transform-origin: center;
        display: inline-block;
      }
      
      /* 导入自定义字体 */
    @font-face {
      font-family: "Bradford";
      src: url("//static.aoneandatwodesign.com/static/fonts/BradfordLL-Book.otf") format("opentype");
    }

    @font-face {
      font-family: "SourceHanSerifCN-Regular";
      src: url("//static.aoneandatwodesign.com/static/fonts/SourceHanSerifCN-Regular.otf") format("opentype");
    }
    </style>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              // 设置字体优先级
            mixed: ['Bradford', 'SourceHanSerifCN-Regular', 'serif'],
            },
          // 添加选中文本的样式
          textSelection: {
            'selected': {
              backgroundColor: '#000',
              color: '#fff'
            }
          }
          },
        },
      };
    </script>
  </head>

  <body class="base-1920 bg-white text-black font-mixed font-light">
    <!-- 导航栏 -->
    <nav class="w-full antialiased" id="main-nav">
      <div class="flex justify-between items-center text-[clamp(1.5rem,3vw,2rem)] leading-[clamp(1.75rem,3vw,1.75rem)]  mt-[clamp(2.8125rem,5vw,2.8125rem)] mobile-padding">
        <div class="flex-shrink-0">
          <a href="/">
            <span style="letter-spacing: -0.05em">a</span
            ><span style="letter-spacing: -0.03em">O</span
            ><span style="letter-spacing: -0.04em">n</span
            ><span style="letter-spacing: -0.03em">e</span
            ><span style="letter-spacing: -0.03em">a</span
            ><span style="letter-spacing: -0.04em">n</span
            ><span style="letter-spacing: -0.03em">d</span
            ><span style="letter-spacing: -0.122em">a</span
            ><span style="letter-spacing: -0.14em">T</span
            ><span style="letter-spacing: -0.04em">w</span>o
          </a>
        </div>
        <!-- 桌面导航 -->
        <div class="hidden md:flex" >
          <a href="/works" class="mr-[clamp(2.4375rem,3vw,2.4375rem)]">work</a>
          <a href="/list" class="mr-[clamp(2.125rem,3vw,2.125rem)]">index</a>
          <a href="/about" class="">about</a>
        </div>
        <!-- 移动端菜单按钮 -->
        <button id="menu-toggle" class="md:hidden text-2xl">
          <i class="fa fa-bars"></i>
        </button>
      </div>
      <!-- 移动端导航菜单 -->
      <div id="mobile-menu" class="hidden md:hidden bg-white absolute w-full z-10 shadow-lg mobile-padding">
        <div class="flex flex-col py-4">
          <a href="/works" class="py-2">work</a>
          <a href="/about" class="py-2">about</a>
        </div>
      </div>
    </nav>

    <!-- 作品展示区域 -->
    <main class="md:px-[clamp(1rem,15.625vw,108.75rem)] mobile-padding mt-[clamp(4rem,5vw,4.3rem)]">
      
      <div class="container mx-0 px-0 py-8 max-w-none">
        <!-- 上部布局 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <!-- 左上部分 -->
            <div class="h-full">
              {{if .EnTitle}}
              <p class="text-[26px] leading-[32px] antialiased">
                {{ nl2br .EnTitle}}
              </p>
              {{end}}
              {{if .ChTitle}}
              <p class="text-[22px] leading-[32px] tracking-[5.5%] text-cn ">
                {{ nl2br .ChTitle}}
              </p>
              {{end}}
            </div>
            
            <!-- 右上部分 -->
            {{if .Introduction}}
            <div class="prose prose-lg max-w-none">
              <p class="text-[16px] leading-[29px] tracking-[-4%] text-cn ">
                {{ nl2br .Introduction}}
              </p>
            </div>
            {{end}}
        </div>
        
        <!-- 下部布局 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- 左下部分 -->
            {{if .DetailContent}}
            <div class="space-y-2">
              <div class="text-[18px] leading-[27px] md:leading-[21px] antialiased">
                {{ nl2br .Content}}
              </div>
            </div>
            {{end}}
            
            <!-- 右下部分 -->
            {{if .Type}}
            <div class="flex">
              <p class="text-[18px] antialiased">
                {{ nl2br .Type}}
              </p>
            </div>
            {{end}}
        </div>
    </div>

    <!-- --- -->

        
        <div class="flex flex-wrap mt-[clamp(2rem,5vw,5.62rem)] gap-x-[clamp(1rem,2vw,1.68rem)] gap-y-[clamp(1rem,2vw,1.62rem)]">
          
        {{ range $index, $image := .Photos }}
          {{if hasSuffix $image ".mp4"}}
            <video 
              class="w-full h-auto object-cover aspect-[1322/883]"
              src="{{$image}}"
              autoplay
              loop
              muted
              playsinline
            >
              <source src="{{$image}}" type="video/mp4">
            </video>
          {{else}}
            <img
              class="w-full h-auto object-cover aspect-[1322/883]"
              src="{{$image}}"
              alt="{{$index}}"
            />
          {{end}}
          {{end}}
        </div>
    </main>

  <!-- 页脚 -->
  <footer class="pt-[clamp(3rem,10vw,168px)] pb-[clamp(3rem,15vw,242px)]">
    <div class="mx-auto">
      <p class="text-center text-[clamp(0.75rem,1vw,14px)] leading-[clamp(1rem,1.5vw,20px)] antialiased">
        © 2019-2025 a one and a two design. All rights reserved
      </p>
    </div>
  </footer>

  <!-- 回到顶部按钮 -->
  <button onclick="scrollToTop()" id="backToTop" class="fixed bottom-[clamp(1.5rem,2vw,56px)] right-[clamp(1.5rem,2vw,56px)] w-[clamp(3rem,5vw,3.5rem)] h-[clamp(3rem,5vw,3.5rem)] flex items-center justify-center   mix-blend-exclusion">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="240 380 50 80" class="w-[clamp(3rem,5vw,3.5rem)] h-[clamp(3rem,5vw,3.5rem)]">
      <path d="M263.79,389.4l14.51,15.38h-8.76v24.72h-11.51v-24.72h-8.76L263.79,389.4z M263.79,386.93l-18.48,19.55h11.03v24.72h14.9v-24.72h11.03L263.79,386.93z" fill="white" />
    </svg>
  </button>


  <script>
    // 移动端菜单切换
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    
    menuToggle.addEventListener('click', () => {
      mobileMenu.classList.toggle('hidden');
    });

    // 控制回到顶部按钮的显示和隐藏
    window.onscroll = function() {
      const backToTopButton = document.getElementById('backToTop');
      if (document.body.scrollTop > 200 || document.documentElement.scrollTop > 200) {
        backToTopButton.classList.remove('invisible');
        backToTopButton.classList.add('visible');
      } else {
        backToTopButton.classList.remove('visible');
        backToTopButton.classList.add('invisible');
      }
    };

    // 平滑滚动到顶部
    function scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  </script>

</body>

</html>