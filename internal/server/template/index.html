<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>A ONE AND A TWO DESIGN</title>
  <script src="//static.aoneandatwodesign.com/static/style/tailwind.js"></script>
  <link rel="stylesheet" href="//static.aoneandatwodesign.com/static/style/all.min.css">

  <style type="text/tailwindcss">
    @layer utilities {
      /* 轮播容器基础样式 */
      .carousel-container {
        overflow: hidden;
        height: 100vh; /* 固定高度 */
      }

      .carousel-slide {
        flex-shrink: 0;
        width: 100%;
        height: 100%;
        display: none; /* 初始隐藏所有图片 */
      }

      .carousel-slide.active {
        display: block; /* 显示当前图片 */
      }

      .content-auto {
        content-visibility: auto;
      }
      .global-selection {
        @apply selection:bg-black selection:text-white;
      }
      .mobile-padding {
        @apply px-[clamp(1rem,3vw,3.5rem)];
      }
      .mobile-column {
        @apply flex-col md:flex-row;
      }
      .adaptive-spacing {
        @apply mt-[clamp(1rem,2vw,1.5rem)] md:mt-0 md:ml-[clamp(1.5rem,3vw,2.625rem)];
      }
      .column-spacing {
        @apply lg:pr-[clamp(2rem,4vw,5rem)];
      }
      .mobile-divider {
        @apply md:hidden border-t border-gray-200 my-[clamp(2rem,5vw,3rem)];
      }
    }
    body {
      @apply global-selection;
    }

    .text-cn {
      transform: scaleY(0.95);
      transform-origin: center;
      display: inline-block;
    }

    /* 导入自定义字体 */
    @font-face {
      font-family: "Bradford";
      src: url("//static.aoneandatwodesign.com/static/fonts/BradfordLL-Book.otf") format("opentype");
    }

    @font-face {
      font-family: "SourceHanSerifCN-Regular";
      src: url("//static.aoneandatwodesign.com/static/fonts/SourceHanSerifCN-Regular.otf") format("opentype");
    }
  </style>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            // 设置字体优先级
            mixed: ['Bradford', 'SourceHanSerifCN-Regular', 'serif'],
          },
          // 添加选中文本的样式
          textSelection: {
            'selected': {
              backgroundColor: '#000',
              color: '#fff'
            }
          }
        },
      },
    };
  </script>
</head>

<body class="base-1920 text-black overflow-hidden font-mixed font-light">
<!-- 导航栏 -->
<nav class="z-50 w-full fixed top-0 pb-4 text-white mix-blend-exclusion antialiased" id="main-nav">
  <div class="flex justify-between items-center  text-[clamp(1.5rem,3vw,2rem)] leading-[clamp(1.75rem,3vw,1.75rem)] mt-[clamp(2.8125rem,5vw,2.8125rem)] mobile-padding">
    <div class="flex-shrink-0">
      <a href="index.html">
          <span style="letter-spacing: -0.05em">a</span
          ><span style="letter-spacing: -0.03em">O</span
      ><span style="letter-spacing: -0.04em">n</span
      ><span style="letter-spacing: -0.03em">e</span
      ><span style="letter-spacing: -0.03em">a</span
      ><span style="letter-spacing: -0.04em">n</span
      ><span style="letter-spacing: -0.03em">d</span
      ><span style="letter-spacing: -0.122em">a</span
      ><span style="letter-spacing: -0.14em">T</span
      ><span style="letter-spacing: -0.04em">w</span>o
      </a>
    </div>
    <!-- 桌面导航 -->
    <div class="hidden md:flex" >
      <a href="/works" class="mr-[clamp(2.4375rem,3vw,2.4375rem)]">work</a>
      <a href="/list" class="mr-[clamp(2.125rem,3vw,2.125rem)]">index</a>
      <a href="/about" class="">about</a>
    </div>
    <!-- 移动端菜单按钮 -->
    <button id="menu-toggle" class="md:hidden text-2xl">
      <i class="fa fa-bars"></i>
    </button>
  </div>
  <!-- 移动端导航菜单 -->
  <div id="mobile-menu" class="hidden md:hidden  absolute w-full z-10 shadow-lg mobile-padding">
    <div class="flex flex-col py-4">
      <a href="/works" class="py-2">work</a>
      <a href="/about" class="py-2">about</a>
    </div>
  </div>
</nav>


<!-- 作品展示区域（自动轮播容器） -->
<main class="h-screen w-screen relative">
  <div class="carousel-container relative">
    {{range $index, $image := .BannerImages}}
    <div class="carousel-slide {{if eq $index 0}}active{{end}}" data-index="{{$index}}">
      {{if hasSuffix $image.URL ".mp4"}}
        <video src="{{$image.URL}}" class="w-full h-full object-cover" autoplay loop muted playsinline>
          <source src="{{$image.URL}}" type="video/mp4">
        </video>
      {{else}}
        <img src="{{$image.URL}}" class="w-full h-full object-cover" alt="{{$image.Alt}}">
      {{end}}
    </div>
    {{end}}
  </div>
</main>

<!-- 页脚 -->
<footer class="fixed text-white mix-blend-exclusion bottom-0 w-full py-8 z-50">
  <p class="text-center  text-[14px] leading-[20px] font-regular antialiased">
    © 2019-2025 a one and a two design. All rights reserved
  </p>
</footer>

<script>
  // 移动端菜单切换
  const menuToggle = document.getElementById('menu-toggle');
  const mobileMenu = document.getElementById('mobile-menu');

  menuToggle.addEventListener('click', () => {
    mobileMenu.classList.toggle('hidden');
  });

  let currentIndex = 0;
  const slides = document.querySelectorAll('.carousel-slide');
  const totalSlides = slides.length;

  // 初始化显示第一张图片
  function showSlide(index) {
    slides.forEach(slide => slide.classList.remove('active'));
    slides[index].classList.add('active');
    currentIndex = index;
  }

  // 自动切换图片
  function autoSlide() {
    currentIndex = (currentIndex + 1) % totalSlides;
    showSlide(currentIndex);
  }

  // 初始化并启动自动轮播
  showSlide(0);
  setInterval(autoSlide, 3000); // 3秒切换间隔
</script>
</body>

</html>