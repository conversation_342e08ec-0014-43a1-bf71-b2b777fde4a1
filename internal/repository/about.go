package repository

import (
	"context"
	"errors"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/model"

	"gorm.io/gorm"
)

type AboutRepository interface {
	GetAbout(ctx context.Context) (*model.About, error)
	CreateAbout(ctx context.Context, v *v1.SaveAboutRequest) error
	UpdateAbout(ctx context.Context, v *v1.UpdateAboutRequest) error
}

func NewAboutRepository(
	repository *Repository,
) AboutRepository {
	return &aboutRepository{
		Repository: repository,
	}
}

type aboutRepository struct {
	*Repository
}

func (r *aboutRepository) GetAbout(ctx context.Context) (*model.About, error) {
	var about model.About

	err := r.DB(ctx).Limit(1).First(&about).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 未找到记录时返回空对象
			return &model.About{}, nil
		}
		// 其他错误（如数据库连接问题）仍返回错误
		return nil, err
	}
	return &about, nil
}

func (r *aboutRepository) CreateAbout(ctx context.Context, v *v1.SaveAboutRequest) error {
	about := &model.About{
		Img:     v.Img,
		ChTitle: v.ChTitle,
		EnTitle: v.EnTitle,
		Email:   v.Email,
		Address: v.Address,
	}
	for _, item := range v.FollowUs {
		about.FollowUs = append(about.FollowUs, model.FollowUsItem{
			Name: item.Name,
			Link: item.Link,
			Sort: item.Sort,
		})
	}
	return r.DB(ctx).Create(about).Error
}

func (r *aboutRepository) UpdateAbout(ctx context.Context, v *v1.UpdateAboutRequest) error {
	about := &model.About{
		Img:     v.Img,
		ChTitle: v.ChTitle,
		EnTitle: v.EnTitle,
		Email:   v.Email,
		Address: v.Address,
	}

	for _, item := range v.FollowUs {
		about.FollowUs = append(about.FollowUs, model.FollowUsItem{
			Name: item.Name,
			Link: item.Link,
			Sort: item.Sort,
		})
	}
	return r.DB(ctx).Where("id = ?", v.Id).Select("*").Updates(about).Error
}
