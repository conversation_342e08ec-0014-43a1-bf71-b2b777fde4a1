package repository

import (
	"context"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/model"
)

type CategoryRepository interface {
	GetCategory(ctx context.Context, id int64) (*model.Category, error)
	GetCategories(ctx context.Context, ids []int64) ([]*model.Category, error)
	CreateCategory(ctx context.Context, category *v1.SaveCategoryRequest) error
	UpdateCategory(ctx context.Context, category *v1.UpdateCategoryRequest) error
	ListCategory(ctx context.Context, req *v1.ListCategoryRequest) ([]*model.Category, int64, error)
	DeleteCategory(ctx context.Context, id int) error
}

func NewCategoryRepository(
	repository *Repository,
) CategoryRepository {
	return &categoryRepository{
		Repository: repository,
	}
}

type categoryRepository struct {
	*Repository
}

func (r *categoryRepository) GetCategory(ctx context.Context, id int64) (*model.Category, error) {
	var category model.Category
	err := r.DB(ctx).First(&category, id).Error
	return &category, err
}

func (r *categoryRepository) GetCategories(ctx context.Context, ids []int64) ([]*model.Category, error) {
	var category = []*model.Category{}
	err := r.DB(ctx).Where("id in (?)", ids).Find(&category).Error
	return category, err
}

func (r *categoryRepository) CreateCategory(ctx context.Context, v *v1.SaveCategoryRequest) error {

	Category := &model.Category{
		TypeName: v.TypeName,
		Sort:     v.Sort,
	}
	return r.DB(ctx).Create(Category).Error
}

func (r *categoryRepository) UpdateCategory(ctx context.Context, v *v1.UpdateCategoryRequest) error {

	Category := &model.Category{
		TypeName: v.TypeName,
		Sort:     v.Sort,
	}
	return r.DB(ctx).Where("id = ?", v.Id).Select("*").Updates(Category).Error
}

func (r *categoryRepository) ListCategory(ctx context.Context, req *v1.ListCategoryRequest) ([]*model.Category, int64, error) {
	db := r.DB(ctx).Model(&model.Category{})
	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 查询列表
	var list []*model.Category

	total, err := paginate(db, req.Page, req.PageSize, &list, "sort DESC")
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

func (r *categoryRepository) DeleteCategory(ctx context.Context, id int) error {

	return r.DB(ctx).Delete(&model.Category{}, id).Error
}
