package repository

import (
	"context"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/model"
)

type MessageIndexDetailRepository interface {
	GetMessageIndexDetail(ctx context.Context, indexId int) (*v1.MessageIndexImgResponse, error)
	CreateOrUpdateMessageIndexDetail(ctx context.Context, v *v1.CreateOrUpdateDetailRequest) error
}

func NewMessageIndexDetailRepository(
	repository *Repository,
) MessageIndexDetailRepository {
	return &messageIndexDetailRepository{
		Repository: repository,
	}
}

type messageIndexDetailRepository struct {
	*Repository
}

func (r *messageIndexDetailRepository) GetMessageIndexDetail(ctx context.Context, indexId int) (*v1.MessageIndexImgResponse, error) {
	var details []model.MessageIndexDetail

	// 查询所有与 indexId 关联的记录
	if err := r.db.Where("index_id = ?", indexId).Find(&details).Error; err != nil {
		return nil, err
	}

	// 转换为响应格式
	response := &v1.MessageIndexImgResponse{
		IndexId: indexId,
		ImgArr:  make([]*v1.MessageIndexImgItem, len(details)),
	}

	for i, detail := range details {
		response.ImgArr[i] = &v1.MessageIndexImgItem{
			Sort: detail.Sort,
			Img:  detail.Img,
		}
	}

	return response, nil
}
func (r *messageIndexDetailRepository) CreateOrUpdateMessageIndexDetail(ctx context.Context, v *v1.CreateOrUpdateDetailRequest) error {
	// 1. 根据 IndexId 删除现有数据
	if err := r.db.Where("index_id = ?", v.IndexId).Delete(&model.MessageIndexDetail{}).Error; err != nil {
		return err
	}

	// 2. 逐条插入新数据
	for _, item := range v.ImgArr {
		detail := &model.MessageIndexDetail{
			IndexId: v.IndexId,
			Sort:    item.Sort,
			Img:     item.Img,
		}

		if err := r.db.Create(detail).Error; err != nil {
			return err
		}
	}

	return nil
}
