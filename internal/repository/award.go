package repository

import (
	"context"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/model"
)

type AwardRepository interface {
	GetAward(ctx context.Context, id int64) (*model.Award, error)
	GetAwards(ctx context.Context, ids []int64) ([]*model.Award, error)
	CreateAward(ctx context.Context, award *v1.SaveAwardRequest) error
	UpdateAward(ctx context.Context, award *v1.UpdateAwardRequest) error
	ListAward(ctx context.Context, req *v1.ListAwardRequest) ([]*model.Award, int64, error)
	DeleteAward(ctx context.Context, id int) error
}

func NewAwardRepository(
	repository *Repository,
) AwardRepository {
	return &awardRepository{
		Repository: repository,
	}
}

type awardRepository struct {
	*Repository
}

func (r *awardRepository) GetAward(ctx context.Context, id int64) (*model.Award, error) {
	var award model.Award
	err := r.DB(ctx).First(&award, id).Error
	return &award, err
}

func (r *awardRepository) GetAwards(ctx context.Context, ids []int64) ([]*model.Award, error) {
	var awards = []*model.Award{}
	err := r.DB(ctx).Where("id in (?)", ids).Find(&awards).Error
	return awards, err
}

func (r *awardRepository) CreateAward(ctx context.Context, v *v1.SaveAwardRequest) error {
	award := &model.Award{
		Name: v.Name,
		Year: v.Year,
		Work: v.Work,
	}
	return r.DB(ctx).Create(award).Error
}

func (r *awardRepository) UpdateAward(ctx context.Context, v *v1.UpdateAwardRequest) error {
	award := &model.Award{
		Name: v.Name,
		Year: v.Year,
		Work: v.Work,
	}
	return r.DB(ctx).Where("id = ?", v.ID).Select("*").Updates(award).Error
}

func (r *awardRepository) ListAward(ctx context.Context, req *v1.ListAwardRequest) ([]*model.Award, int64, error) {
	db := r.DB(ctx).Model(&model.Award{})
	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 查询列表
	var list []*model.Award

	total, err := paginate(db, req.Page, req.PageSize, &list, "year DESC")
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

func (r *awardRepository) DeleteAward(ctx context.Context, id int) error {
	return r.DB(ctx).Delete(&model.Award{}, id).Error
}
