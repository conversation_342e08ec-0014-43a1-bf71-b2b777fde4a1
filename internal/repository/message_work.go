package repository

import (
	"context"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/model"
)

type MessageWorkRepository interface {
	Create(ctx context.Context, req *v1.CreateMessageWorkRequest) error
	Update(ctx context.Context, v *v1.UpdateMessageWorkRequest) error
	Delete(ctx context.Context, req *v1.DeleteMessageWorkRequest) error
	List(ctx context.Context, req *v1.ListMessageWorkRequest) ([]*model.MessageWork, int64, error)
	Detail(ctx context.Context, id uint) (*model.MessageWork, error)
}

func NewMessageWorkRepository(
	repository *Repository,
) MessageWorkRepository {
	return &messageWorkRepository{
		Repository: repository,
	}
}

type messageWorkRepository struct {
	*Repository
}

func (r *messageWorkRepository) Create(ctx context.Context, req *v1.CreateMessageWorkRequest) error {
	work := &model.MessageWork{
		Url:  req.Url,
		Sort: req.Sort,
	}

	return r.DB(ctx).Create(work).Error
}

func (r *messageWorkRepository) Update(ctx context.Context, v *v1.UpdateMessageWorkRequest) error {

	MessageWork := &model.MessageWork{
		Url:  v.Url,
		Sort: v.Sort,
	}
	return r.DB(ctx).Where("id = ?", v.Id).Select("*").Updates(MessageWork).Error
}
func (r *messageWorkRepository) Delete(ctx context.Context, req *v1.DeleteMessageWorkRequest) error {
	return r.DB(ctx).Where("id = ?", req.Id).Delete(&model.MessageWork{}).Error
}
func (r *messageWorkRepository) List(ctx context.Context, req *v1.ListMessageWorkRequest) ([]*model.MessageWork, int64, error) {
	db := r.DB(ctx).Model(&model.MessageWork{})

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 查询列表
	var list []*model.MessageWork

	total, err := paginate(db, req.Page, req.PageSize, &list, "sort DESC")
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

func (r *messageWorkRepository) Detail(ctx context.Context, id uint) (*model.MessageWork, error) {
	var item model.MessageWork
	err := r.DB(ctx).First(&item, id).Error
	return &item, err
}
