package repository

import (
	"context"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/model"
)

type MemberRepository interface {
	GetMember(ctx context.Context, id int64) (*model.Member, error)
	CreateMember(ctx context.Context, category *v1.SaveMemberRequest) error
	UpdateMember(ctx context.Context, v *v1.UpdateMemberRequest) error
	DeleteMember(ctx context.Context, id int) error
	ListMember(ctx context.Context, req *v1.ListMemberRequest) ([]*model.Member, int64, error)
}

func NewMemberRepository(
	repository *Repository,
) MemberRepository {
	return &memberRepository{
		Repository: repository,
	}
}

type memberRepository struct {
	*Repository
}

func (r *memberRepository) GetMember(ctx context.Context, id int64) (*model.Member, error) {
	var member model.Member

	return &member, nil
}
func (r *memberRepository) CreateMember(ctx context.Context, v *v1.SaveMemberRequest) error {

	Member := &model.Member{
		Avatar: v.Avatar,
		BioEn:  v.BioEn,
		BioCn:  v.BioCn,
	}
	return r.DB(ctx).Create(Member).Error
}

func (r *memberRepository) UpdateMember(ctx context.Context, v *v1.UpdateMemberRequest) error {

	Member := &model.Member{
		Avatar: v.Avatar,
		BioEn:  v.BioEn,
		BioCn:  v.BioCn,
	}
	return r.DB(ctx).Where("id = ?", v.Id).Select("*").Updates(Member).Error
}

func (r *memberRepository) DeleteMember(ctx context.Context, id int) error {
	return r.DB(ctx).Delete(&model.Member{}, id).Error
}

func (r *memberRepository) ListMember(ctx context.Context, req *v1.ListMemberRequest) ([]*model.Member, int64, error) {
	var list []*model.Member
	db := r.DB(ctx).Model(&model.Member{})

	// 获取分页数据和总数
	total, err := paginate(db, req.Page, req.PageSize, &list, "id DESC")
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}
