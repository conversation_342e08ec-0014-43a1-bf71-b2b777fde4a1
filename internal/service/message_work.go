package service

import (
	"context"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/model"
	"nunu-layout-admin/internal/repository"
)

type MessageWorkService interface {
	Create(ctx context.Context, req *v1.CreateMessageWorkRequest) error
	Update(ctx context.Context, req *v1.UpdateMessageWorkRequest) error
	Delete(ctx context.Context, req *v1.DeleteMessageWorkRequest) error
	List(ctx context.Context, req *v1.ListMessageWorkRequest) ([]*model.MessageWork, int64, error)
	Detail(ctx context.Context, id uint) (*model.MessageWork, error)
}

func NewMessageWorkService(
	service *Service,
	messageWorkRepository repository.MessageWorkRepository,
) MessageWorkService {
	return &messageWorkService{
		Service:               service,
		messageWorkRepository: messageWorkRepository,
	}
}

type messageWorkService struct {
	*Service
	messageWorkRepository repository.MessageWorkRepository
}

// 2. 实现Create方法
func (s *messageWorkService) Create(ctx context.Context, req *v1.CreateMessageWorkRequest) error {
	return s.messageWorkRepository.Create(ctx, req)
}
func (s *messageWorkService) Update(ctx context.Context, req *v1.UpdateMessageWorkRequest) error {
	return s.messageWorkRepository.Update(ctx, req)
}
func (s *messageWorkService) Delete(ctx context.Context, req *v1.DeleteMessageWorkRequest) error {
	return s.messageWorkRepository.Delete(ctx, req)
}
func (s *messageWorkService) List(ctx context.Context, req *v1.ListMessageWorkRequest) ([]*model.MessageWork, int64, error) {
	return s.messageWorkRepository.List(ctx, req)
}

func (s *messageWorkService) Detail(ctx context.Context, id uint) (*model.MessageWork, error) {
	return s.messageWorkRepository.Detail(ctx, id)
}
