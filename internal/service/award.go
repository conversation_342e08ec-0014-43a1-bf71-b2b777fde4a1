package service

import (
	"context"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/model"
	"nunu-layout-admin/internal/repository"
)

type AwardService interface {
	GetAward(ctx context.Context, id int64) (*model.Award, error)
	CreateAward(ctx context.Context, req *v1.SaveAwardRequest) error
	UpdateAward(ctx context.Context, req *v1.UpdateAwardRequest) error
	ListAward(ctx context.Context, req *v1.ListAwardRequest) ([]*model.Award, int64, error)
	DeleteAward(ctx context.Context, req *v1.DeleteAwardRequest) error
}

func NewAwardService(
	service *Service,
	awardRepository repository.AwardRepository,
) AwardService {
	return &awardService{
		Service:         service,
		awardRepository: awardRepository,
	}
}

type awardService struct {
	*Service
	awardRepository repository.AwardRepository
}

func (s *awardService) GetAward(ctx context.Context, id int64) (*model.Award, error) {
	return s.awardRepository.GetAward(ctx, id)
}

func (s *awardService) CreateAward(ctx context.Context, req *v1.SaveAwardRequest) error {
	return s.awardRepository.CreateAward(ctx, req)
}

func (s *awardService) UpdateAward(ctx context.Context, req *v1.UpdateAwardRequest) error {
	return s.awardRepository.UpdateAward(ctx, req)
}

func (s *awardService) ListAward(ctx context.Context, req *v1.ListAwardRequest) ([]*model.Award, int64, error) {
	return s.awardRepository.ListAward(ctx, req)
}

func (s *awardService) DeleteAward(ctx context.Context, req *v1.DeleteAwardRequest) error {
	return s.awardRepository.DeleteAward(ctx, req.ID)
}
