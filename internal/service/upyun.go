package service

import (
	"context"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/spf13/viper"
)

type UpyunService interface {
	GenerateSignature(ctx context.Context, saveKey string) (map[string]string, error)
}

func NewUpyunService(
	service *Service,
	conf *viper.Viper,

) UpyunService {
	return &upyunService{
		Service: service,
		conf:    conf,
	}
}

type upyunService struct {
	*Service
	conf *viper.Viper
}

func (s *upyunService) GenerateSignature(ctx context.Context, saveKey string) (map[string]string, error) {
	if saveKey == "" {
		return nil, errors.New("saveKey is required")
	}
	bucket := s.conf.GetString("upyun.bucket")
	operator := s.conf.GetString("upyun.operator")
	password := s.conf.GetString("upyun.password")

	expireTime := time.Now().Add(time.Minute * 30).Unix()

	// 构建策略
	policy := map[string]interface{}{
		"bucket":               bucket,
		"save-key":             saveKey,
		"expiration":           expireTime,
		"content-length-range": "0,1073741824",
	}

	// 编码 Policy
	policyJSON, err := json.Marshal(policy)
	if err != nil {
		return nil, fmt.Errorf("marshal policy failed: %w", err)
	}
	encodedPolicy := base64.StdEncoding.EncodeToString(policyJSON)

	// 1. 对密码进行 MD5 加密
	md5Hash := md5.Sum([]byte(password))
	md5Password := hex.EncodeToString(md5Hash[:])

	// 2. 构建完整消息：HTTP方法&URI&encodedPolicy
	uri := fmt.Sprintf("/%s", bucket)
	message := fmt.Sprintf("POST&%s&%s", uri, encodedPolicy) // 修正消息格式

	// 3. 使用 HMAC-SHA1 算法
	h := hmac.New(sha1.New, []byte(md5Password))
	h.Write([]byte(message))
	signatureBytes := h.Sum(nil)

	// 4. 对结果进行 Base64 编码
	signature := fmt.Sprintf("UpYun %s:%s", operator, base64.StdEncoding.EncodeToString(signatureBytes))

	return map[string]string{
		"policy":        encodedPolicy,
		"authorization": signature,
		"bucket":        bucket,
		"save_key":      saveKey,
		"expire":        fmt.Sprintf("%d", expireTime),
	}, nil
}
