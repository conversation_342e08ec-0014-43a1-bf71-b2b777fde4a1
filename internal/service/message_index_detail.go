package service

import (
	"context"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/repository"
)

type MessageIndexDetailService interface {
	GetMessageIndexDetail(ctx context.Context, indexId int) (*v1.MessageIndexImgResponse, error)
	CreateOrUpdateMessageIndexDetail(ctx context.Context, req *v1.CreateOrUpdateDetailRequest) error
}

func NewMessageIndexDetailService(
	service *Service,
	messageIndexDetailRepository repository.MessageIndexDetailRepository,
) MessageIndexDetailService {
	return &messageIndexDetailService{
		Service:                      service,
		messageIndexDetailRepository: messageIndexDetailRepository,
	}
}

type messageIndexDetailService struct {
	*Service
	messageIndexDetailRepository repository.MessageIndexDetailRepository
}

func (s *messageIndexDetailService) GetMessageIndexDetail(ctx context.Context, indexId int) (*v1.MessageIndexImgResponse, error) {
	return s.messageIndexDetailRepository.GetMessageIndexDetail(ctx, indexId)
}

func (s *messageIndexDetailService) CreateOrUpdateMessageIndexDetail(ctx context.Context, req *v1.CreateOrUpdateDetailRequest) error {
	return s.messageIndexDetailRepository.CreateOrUpdateMessageIndexDetail(ctx, req)
}
