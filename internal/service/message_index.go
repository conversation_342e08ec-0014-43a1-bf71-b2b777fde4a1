package service

import (
	"context"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/repository"
)

type MessageIndexService interface {
	MessageIndexCreate(ctx context.Context, req *v1.SaveMessageIndexRequest) error
	MessageIndexUpdate(ctx context.Context, req *v1.UpdateMessageIndexRequest) error
	MessageIndexDelete(ctx context.Context, req *v1.DeleteMessageIndexRequest) error
	MessageIndexList(ctx context.Context, req *v1.MessageIndexListRequest) (*v1.MessageIndexListResponse, error)
	MessageIndexDetail(ctx context.Context, id uint) (*v1.MessageIndexDetailResponse, error)
	MessageIndexUpdateDetail(ctx context.Context, req *v1.UpdateMessageIndexDetailRequest) error
}

func NewMessageIndexService(
	service *Service,
	messageIndexRepository repository.MessageIndexRepository,
) MessageIndexService {
	return &messageIndexService{
		Service:                service,
		messageIndexRepository: messageIndexRepository,
	}
}

type messageIndexService struct {
	*Service
	messageIndexRepository repository.MessageIndexRepository
}

func (s *messageIndexService) MessageIndexCreate(ctx context.Context, req *v1.SaveMessageIndexRequest) error {
	return s.messageIndexRepository.Create(ctx, req)
}
func (s *messageIndexService) MessageIndexUpdate(ctx context.Context, req *v1.UpdateMessageIndexRequest) error {
	return s.messageIndexRepository.Update(ctx, req)
}

func (s *messageIndexService) MessageIndexUpdateDetail(ctx context.Context, req *v1.UpdateMessageIndexDetailRequest) error {
	return s.messageIndexRepository.UpdateDetail(ctx, req)
}
func (s *messageIndexService) MessageIndexDelete(ctx context.Context, req *v1.DeleteMessageIndexRequest) error {
	return s.messageIndexRepository.Delete(ctx, req.Id)
}

func (s *messageIndexService) MessageIndexList(ctx context.Context, req *v1.MessageIndexListRequest) (*v1.MessageIndexListResponse, error) {
	return s.messageIndexRepository.List(ctx, req)
}

func (s *messageIndexService) MessageIndexDetail(ctx context.Context, id uint) (*v1.MessageIndexDetailResponse, error) {
	return s.messageIndexRepository.Detail(ctx, id)
}
