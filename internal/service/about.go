package service

import (
	"context"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/model"
	"nunu-layout-admin/internal/repository"
)

type AboutService interface {
	GetAbout(ctx context.Context) (*model.About, error)
	CreateAbout(ctx context.Context, req *v1.SaveAboutRequest) error
	UpdateAbout(ctx context.Context, req *v1.UpdateAboutRequest) error
}

func NewAboutService(
	service *Service,
	aboutRepository repository.AboutRepository,
) AboutService {
	return &aboutService{
		Service:         service,
		aboutRepository: aboutRepository,
	}
}

type aboutService struct {
	*Service
	aboutRepository repository.AboutRepository
}

func (s *aboutService) GetAbout(ctx context.Context) (*model.About, error) {
	return s.aboutRepository.GetAbout(ctx)
}

func (s *aboutService) UpdateAbout(ctx context.Context, req *v1.UpdateAboutRequest) error {
	return s.aboutRepository.UpdateAbout(ctx, req)
}
func (s *aboutService) CreateAbout(ctx context.Context, req *v1.SaveAboutRequest) error {
	return s.aboutRepository.CreateAbout(ctx, req)
}
