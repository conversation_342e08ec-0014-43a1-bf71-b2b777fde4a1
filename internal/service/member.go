package service

import (
	"context"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/model"
	"nunu-layout-admin/internal/repository"
)

type MemberService interface {
	GetMember(ctx context.Context, id int64) (*model.Member, error)
	CreateMember(ctx context.Context, req *v1.SaveMemberRequest) error
	UpdateMember(ctx context.Context, req *v1.UpdateMemberRequest) error
	DeleteMember(ctx context.Context, req *v1.DeleteMemberRequest) error
	ListMember(ctx context.Context, req *v1.ListMemberRequest) ([]*model.Member, int64, error)
}

func NewMemberService(
	service *Service,
	memberRepository repository.MemberRepository,
) MemberService {
	return &memberService{
		Service:          service,
		memberRepository: memberRepository,
	}
}

type memberService struct {
	*Service
	memberRepository repository.MemberRepository
}

func (s *memberService) GetMember(ctx context.Context, id int64) (*model.Member, error) {
	return s.memberRepository.GetMember(ctx, id)
}

func (s *memberService) CreateMember(ctx context.Context, req *v1.SaveMemberRequest) error {
	return s.memberRepository.CreateMember(ctx, req)
}

func (s *memberService) UpdateMember(ctx context.Context, req *v1.UpdateMemberRequest) error {
	return s.memberRepository.UpdateMember(ctx, req)
}
func (s *memberService) DeleteMember(ctx context.Context, req *v1.DeleteMemberRequest) error {
	return s.memberRepository.DeleteMember(ctx, req.Id)
}

func (s *memberService) ListMember(ctx context.Context, req *v1.ListMemberRequest) ([]*model.Member, int64, error) {
	return s.memberRepository.ListMember(ctx, req)
}
