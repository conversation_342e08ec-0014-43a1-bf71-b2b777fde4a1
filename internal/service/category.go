package service

import (
	"context"
	v1 "nunu-layout-admin/api/v1"
	"nunu-layout-admin/internal/model"
	"nunu-layout-admin/internal/repository"
)

type CategoryService interface {
	GetCategory(ctx context.Context, id int64) (*model.Category, error)
	CreateCategory(ctx context.Context, req *v1.SaveCategoryRequest) error
	UpdateCategory(ctx context.Context, req *v1.UpdateCategoryRequest) error
	ListCategory(ctx context.Context, req *v1.ListCategoryRequest) ([]*model.Category, int64, error)
	DeleteCategory(ctx context.Context, req *v1.DeleteCategoryRequest) error
}

func NewCategoryService(
	service *Service,
	categoryRepository repository.CategoryRepository,
) CategoryService {
	return &categoryService{
		Service:            service,
		categoryRepository: categoryRepository,
	}
}

type categoryService struct {
	*Service
	categoryRepository repository.CategoryRepository
}

func (s *categoryService) GetCategory(ctx context.Context, id int64) (*model.Category, error) {
	return s.categoryRepository.GetCategory(ctx, id)
}

func (s *categoryService) CreateCategory(ctx context.Context, req *v1.SaveCategoryRequest) error {
	return s.categoryRepository.CreateCategory(ctx, req)
}

func (s *categoryService) UpdateCategory(ctx context.Context, req *v1.UpdateCategoryRequest) error {
	return s.categoryRepository.UpdateCategory(ctx, req)
}
func (s *categoryService) ListCategory(ctx context.Context, req *v1.ListCategoryRequest) ([]*model.Category, int64, error) {
	return s.categoryRepository.ListCategory(ctx, req)
}
func (s *categoryService) DeleteCategory(ctx context.Context, req *v1.DeleteCategoryRequest) error {
	return s.categoryRepository.DeleteCategory(ctx, req.Id)
}
