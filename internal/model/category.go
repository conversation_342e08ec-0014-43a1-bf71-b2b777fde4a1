package model

import "gorm.io/gorm"

type Category struct {
	gorm.Model
	TypeName string `json:"type" gorm:"type:varchar(100);not null;comment:'类型名称'"`
	Sort     int    `json:"sort" gorm:"column:sort;type:int;default:0;comment:排序"`
}

//type MessageIndex struct {
//	gorm.Model
//	Client  string `json:"client" gorm:"type:varchar(100);not null;comment:'链接'"`
//	Type    string `json:"type" gorm:"type:varchar(100);not null;comment:'类型'"`
//	Project string `json:"project" gorm:"type:varchar(100);not null;comment:'项目'"`
//	Year    int    `json:"year" gorm:"column:year;type:int;default:0;comment:年份"`
//}

func (m *Category) TableName() string {
	return "category"
}
