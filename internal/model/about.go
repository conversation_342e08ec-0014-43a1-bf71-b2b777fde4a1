package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"

	"gorm.io/gorm"
)

// FollowUsItem 关注我们的单个项目结构
type FollowUsItem struct {
	Name string `json:"name"`
	Link string `json:"link"`
	Sort int    `json:"sort"`
}

// FollowUsItems 关注我们的项目列表
type FollowUsItems []FollowUsItem

// Value 实现 driver.Valuer 接口
func (f FollowUsItems) Value() (driver.Value, error) {
	return json.Marshal(f)
}

// Scan 实现 sql.Scanner 接口
func (f *FollowUsItems) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		str, ok := value.(string)
		if !ok {
			return fmt.Errorf("无法将类型 %T 转换为 []byte 或 string", value)
		}
		bytes = []byte(str)
	}
	return json.Unmarshal(bytes, &f)
}

type About struct {
	gorm.Model
	//ID      string `json:"id" gorm:"type:varchar(255);not null;comment:'图片路径'"`
	Img      string        `json:"img" gorm:"type:varchar(255);not null;comment:'图片路径'"`
	ChTitle  string        `json:"ch_title" gorm:"type:varchar(100);not null;comment:'中文标签'"`
	EnTitle  string        `json:"en_title" gorm:"type:varchar(100);not null;comment:'英文标签'"`
	Address  string        `json:"address" gorm:"type:varchar(100);not null;comment:'地址'"`
	Email    string        `json:"email" gorm:"type:varchar(100);not null;comment:'邮箱'"`
	FollowUs FollowUsItems `json:"follow_us" gorm:"type:text;not null;comment:'关注我们'"`
}

func (m *About) TableName() string {
	return "about"
}
