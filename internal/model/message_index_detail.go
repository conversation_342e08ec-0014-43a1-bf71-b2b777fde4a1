package model

import "gorm.io/gorm"

type MessageIndexDetail struct {
	gorm.Model
	Img     string `json:"img" gorm:"type:varchar(200);not null;comment:'图片路径'"`
	Sort    int    `json:"sort" gorm:"column:sort;type:int;default:0;comment:排序"`
	IndexId int    `json:"index_id" gorm:"column:index_id;type:int;default:0;comment:作品表id"`
}

func (m *MessageIndexDetail) TableName() string {
	return "message_index_detail"
}
