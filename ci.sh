#docker login -u aoneandatwo-1749042975388 -p 1276bb116a7aa0e85e34db3db825bd83f4acab1a g-pjih2197-docker.pkg.coding.net

# cd web
# npm run build
# cd ..
goos=linux go build -o aoneandatwo cmd/server/main.go

# GIT_VERSION=$(git rev-parse --short HEAD)
# TIME_NOW=$(date +"%Y%m%d%H%M%S")


docker build -t g-pjih2197-docker.pkg.coding.net/guanwangxitong/aoneandatwo/aoneandatwo:latest .
docker push g-pjih2197-docker.pkg.coding.net/guanwangxitong/aoneandatwo/aoneandatwo:latest
read