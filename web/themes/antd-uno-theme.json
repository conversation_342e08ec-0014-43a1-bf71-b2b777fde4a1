{"colors": {"primary": "var(--pro-ant-color-primary)", "success": "var(--pro-ant-color-success)", "warning": "var(--pro-ant-color-warning)", "error": "var(--pro-ant-color-error)", "info": "var(--pro-ant-color-info)", "textBase": "var(--pro-ant-color-text-base)", "bgBase": "var(--pro-ant-color-bg-base)", "text": "var(--pro-ant-color-text)", "textSecondary": "var(--pro-ant-color-text-secondary)", "textTertiary": "var(--pro-ant-color-text-tertiary)", "textQuaternary": "var(--pro-ant-color-text-quaternary)", "fill": "var(--pro-ant-color-fill)", "fillSecondary": "var(--pro-ant-color-fill-secondary)", "fillTertiary": "var(--pro-ant-color-fill-tertiary)", "fillQuaternary": "var(--pro-ant-color-fill-quaternary)", "bgLayout": "var(--pro-ant-color-bg-layout)", "bgContainer": "var(--pro-ant-color-bg-container)", "bgElevated": "var(--pro-ant-color-bg-elevated)", "bgSpotlight": "var(--pro-ant-color-bg-spotlight)", "border": "var(--pro-ant-color-border)", "borderSecondary": "var(--pro-ant-color-border-secondary)", "primaryBg": "var(--pro-ant-color-primary-bg)", "primaryBgHover": "var(--pro-ant-color-primary-bg-hover)", "primaryBorder": "var(--pro-ant-color-primary-border)", "primaryBorderHover": "var(--pro-ant-color-primary-border-hover)", "primaryHover": "var(--pro-ant-color-primary-hover)", "primaryActive": "var(--pro-ant-color-primary-active)", "primaryTextHover": "var(--pro-ant-color-primary-text-hover)", "primaryText": "var(--pro-ant-color-primary-text)", "primaryTextActive": "var(--pro-ant-color-primary-text-active)", "successBg": "var(--pro-ant-color-success-bg)", "successBgHover": "var(--pro-ant-color-success-bg-hover)", "successBorder": "var(--pro-ant-color-success-border)", "successBorderHover": "var(--pro-ant-color-success-border-hover)", "successHover": "var(--pro-ant-color-success-hover)", "successActive": "var(--pro-ant-color-success-active)", "successTextHover": "var(--pro-ant-color-success-text-hover)", "successText": "var(--pro-ant-color-success-text)", "successTextActive": "var(--pro-ant-color-success-text-active)", "errorBg": "var(--pro-ant-color-error-bg)", "errorBgHover": "var(--pro-ant-color-error-bg-hover)", "errorBorder": "var(--pro-ant-color-error-border)", "errorBorderHover": "var(--pro-ant-color-error-border-hover)", "errorHover": "var(--pro-ant-color-error-hover)", "errorActive": "var(--pro-ant-color-error-active)", "errorTextHover": "var(--pro-ant-color-error-text-hover)", "errorText": "var(--pro-ant-color-error-text)", "errorTextActive": "var(--pro-ant-color-error-text-active)", "warningBg": "var(--pro-ant-color-warning-bg)", "warningBgHover": "var(--pro-ant-color-warning-bg-hover)", "warningBorder": "var(--pro-ant-color-warning-border)", "warningBorderHover": "var(--pro-ant-color-warning-border-hover)", "warningHover": "var(--pro-ant-color-warning-hover)", "warningActive": "var(--pro-ant-color-warning-active)", "warningTextHover": "var(--pro-ant-color-warning-text-hover)", "warningText": "var(--pro-ant-color-warning-text)", "warningTextActive": "var(--pro-ant-color-warning-text-active)", "infoBg": "var(--pro-ant-color-info-bg)", "infoBgHover": "var(--pro-ant-color-info-bg-hover)", "infoBorder": "var(--pro-ant-color-info-border)", "infoBorderHover": "var(--pro-ant-color-info-border-hover)", "infoHover": "var(--pro-ant-color-info-hover)", "infoActive": "var(--pro-ant-color-info-active)", "infoTextHover": "var(--pro-ant-color-info-text-hover)", "infoText": "var(--pro-ant-color-info-text)", "infoTextActive": "var(--pro-ant-color-info-text-active)", "bgMask": "var(--pro-ant-color-bg-mask)", "white": "var(--pro-ant-color-white)"}}