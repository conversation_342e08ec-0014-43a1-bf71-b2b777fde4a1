<div align="center"> <a href="https://github.com/antdv-pro/antdv-pro"> <img alt="VbenAdmin Logo" width="200" height="200" src="./public/logo.svg"> </a> <br> <br>


<h1>Antdv Pro</h1>

</div>

![gitee](https://gitee.com/antdv-pro/antdv-pro/badge/star.svg)
![github](https://img.shields.io/github/stars/antdv-pro/antdv-pro?style=social)

**English** | [简体中文](./README.zh-CN.md)


## Introduction

AntdvPro is a complete set of enterprise-level mid-backend front-end/design solutions based on Vue3, Vite4, ant-design-vue4, Pinia, UnoCSS and Typescript. It refers to the design pattern of Ali react version antd-pro, using the latest and most popular The front-end technology stack has built-in dynamic routing, multi-theme, multi-layout and other functions, which can help you quickly build enterprise-level mid-background product prototypes.


## Features

* pnpm: Using the latest pnpm as a package management tool, it can greatly reduce the size of node_modules, speed up the installation speed of packages, and can also share dependencies to reduce disk usage.
* vite: vite as a front-end development tool, it can greatly speed up the start-up speed of the project, and also supports hot updates, which can greatly improve development efficiency.
* vue3: vue3.3.x as the front-end framework, the basic code is written in script-setup, with less code and low maintenance cost.
* nitro mock: Use nitro as the server's mock data, decoupled from the project, and more flexible and easy to use.
* ant-design-vue4: ant-design-vue4 as the UI framework, the author of admin-pro is also a core member of ant-design-vue, which can provide long-term maintenance support.
* pinia: pinia as a state management tool, it can greatly improve the readability and maintainability of the code, and also supports Typescript.
* UnoCSS: Atomic CSS framework, reduce the troubles caused by thinking about some common class names, and improve our development efficiency.
* Code specification: We have encapsulated a set of eslint-based code specification configuration files, which can be used out of the box to unify the problems brought by different teams.
* Theme: The design specifications of antd-pro of the react version are used, and a set of theme modes based on vue are developed. On this basis, some new functions are added to meet various different needs as much as possible.
* Request function: Based on axios, a set of request functions with complete types and some basic interceptor encapsulations are encapsulated. You only need to make corresponding implementation adjustments according to the requirements to meet the different needs of various projects.
* Mobile compatibility: We have tried our best to make the basic framework compatible with the mobile terminal mode, but because our main goal is the enterprise-level mid-background product, we have not made too much adaptation to the mobile terminal. If your project needs to adapt to the mobile terminal, you can refer to our code for corresponding adjustments.


## Preview

[antdv-pro](https://antdv-pro.com) -  Test Account: admin/admin

[antdv-pro-docs](https://docs.antdv-pro.com) - Documentation

## Community

QQ Group: apply wechat group

Wechat: aibayanyu2022

Discord: [discord](https://discord.gg/tPb4G6gXmm)

WeChatGroup: apply wechat group to add author wechat




## Useage

```bash

# Install degit
npm  i -g degit

# Pull the code
degit antdv-pro/antdv-pro [your project name]

#  Switch to the project directory
cd [your project name]

# Install
pnpm install

# Development
pnpm dev
```

## Contribute

We are very welcome to have you participate in our open source project.


**Pull Request:**

1. Fork code!
2. Create your own branch: `git checkout -b feat-xxxx`
3. Submit your changes: `git commit -am 'feat(function): add xxxxx'`
4. Push your branch: `git push origin feat-xxxx`
5. submit`pull request`

Thank you to all the people who already contributed to antdv-pro!

<a href="https://github.com/antdv-pro/antdv-pro/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=antdv-pro/antdv-pro&max=100&columns=15" />
</a>

## Support

If you like our project, you can support us by clicking the "Star" button in the upper right corner. Your support is my motivation. Thank you ~

Thanks to the open source project license provided by [Jetbrains](https://www.jetbrains.com/?from=antdv-pro).

## Sponsor

If you like our project, you can sponsor us to help us maintain the project better.

[Alipay/Wechat](https://docs.antdv-pro.com/other/sponsor.html)
