<template>
  <div class="demo-page">
    <div class="demo-header bg-white shadow-sm border-b border-gray-200 p-4">
      <div class="max-w-7xl mx-auto">
        <h1 class="text-2xl font-bold text-gray-900">栅格化图片布局编辑器演示</h1>
        <p class="text-gray-600 mt-2">基于Vue3 + Ant Design + Quill + GridStack.js开发的栅格化图片布局编辑器</p>
      </div>
    </div>
    
    <div class="demo-content">
      <GridImageEditor 
        :upload-function="handleImageUpload"
        @save="handleSave"
        @export="handleExport"
        @change="handleChange"
      />
    </div>
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { GridImageEditor } from '~@/components/grid-image-editor'

// 模拟图片上传函数
const handleImageUpload = async (file) => {
  try {
    // 模拟上传延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 返回本地URL作为示例
    const imageUrl = URL.createObjectURL(file)
    
    message.success(`图片 ${file.name} 上传成功`)
    return imageUrl
  } catch (error) {
    message.error('图片上传失败')
    throw error
  }
}

// 处理保存
const handleSave = (layout) => {
  console.log('保存布局数据:', layout)
  message.success('布局已保存到控制台')
}

// 处理导出
const handleExport = (layout) => {
  console.log('导出布局数据:', layout)
  message.success('布局已导出，请查看控制台和下载文件')
}

// 处理变化
const handleChange = (layout) => {
  console.log('布局发生变化:', layout)
}
</script>

<style scoped>
.demo-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.demo-content {
  height: calc(100vh - 80px);
}
</style>
