<template>
  <div class="simple-grid-test p-4">
    <h1>简单GridStack测试</h1>

    <div class="mb-4">
      <a-button @click="addSimpleWidget" type="primary">添加简单Widget</a-button>
      <a-button @click="logGridData" class="ml-2">打印栅格数据</a-button>
    </div>

    <div class="grid-container border border-gray-300 rounded" style="height: 400px;">
      <div ref="gridContainer" class="grid-stack h-full"></div>
    </div>

    <div class="mt-4">
      <h3>调试信息:</h3>
      <pre>{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { GridStack } from 'gridstack'
import 'gridstack/dist/gridstack.min.css'

const gridContainer = ref(null)
const gridStack = ref(null)
const debugInfo = ref('等待初始化...')

let widgetCount = 0

const addSimpleWidget = () => {
  if (!gridStack.value) {
    message.error('GridStack未初始化')
    return
  }

  widgetCount++

  const widget = {
    x: 0,
    y: 0,
    w: 4,
    h: 3,
    content: `<div style="background: #f0f0f0; height: 100%; display: flex; align-items: center; justify-content: center; border-radius: 4px;">Widget ${widgetCount}</div>`
  }

  console.log('添加widget:', widget)

  try {
    const result = gridStack.value.addWidget(widget)
    console.log('addWidget结果:', result)
    message.success(`Widget ${widgetCount} 添加成功`)

    debugInfo.value = `最后添加: Widget ${widgetCount}\n时间: ${new Date().toLocaleTimeString()}`
  } catch (error) {
    console.error('添加widget失败:', error)
    message.error('添加失败: ' + error.message)
  }
}

const logGridData = () => {
  if (!gridStack.value) {
    message.error('GridStack未初始化')
    return
  }

  try {
    const data = gridStack.value.save()
    console.log('当前栅格数据:', data)
    debugInfo.value = `栅格数据:\n${JSON.stringify(data, null, 2)}`
  } catch (error) {
    console.error('获取栅格数据失败:', error)
    debugInfo.value = `错误: ${error.message}`
  }
}

onMounted(async () => {
  await nextTick()

  if (gridContainer.value) {
    try {
      console.log('开始初始化GridStack...')

      // 使用最简单的配置测试
      gridStack.value = GridStack.init({
        column: 12,
        cellHeight: 60,
        margin: 5,
        animate: true,
        float: false,
        removable: false,
        resizable: false,
        draggable: false
      }, gridContainer.value)

      console.log('GridStack初始化成功:', gridStack.value)
      debugInfo.value = 'GridStack初始化成功!'

      // 监听事件
      gridStack.value.on('added', (event, items) => {
        console.log('added事件:', event, items)
      })

      gridStack.value.on('change', (event, items) => {
        console.log('change事件:', event, items)
      })

    } catch (error) {
      console.error('GridStack初始化失败:', error)
      debugInfo.value = `初始化失败: ${error.message}`
      message.error('GridStack初始化失败')
    }
  } else {
    console.error('gridContainer.value为空')
    debugInfo.value = 'gridContainer为空'
  }
})
</script>

<style scoped>
.grid-stack {
  background: linear-gradient(90deg, #f0f0f0 1px, transparent 1px),
    linear-gradient(#f0f0f0 1px, transparent 1px);
  background-size: 20px 20px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
