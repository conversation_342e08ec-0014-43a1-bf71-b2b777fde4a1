<template>
  <div class="message-index-container">
    <!-- 顶部工具栏 -->
    <div class="toolbar flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">轮播图配置</h1>
      <div class="flex space-x-2">
        <a-button type="primary" @click="handleAdd">
          <span class="flex items-center">
            <i class="fa fa-plus mr-2"></i>新增
          </span>
        </a-button>
        <a-button @click="refreshData">
          <span class="flex items-center">
            <i class="fa fa-refresh mr-2"></i>刷新
          </span>
        </a-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="loading-mask flex items-center justify-center h-[300px]"
    >
      <div class="flex flex-col items-center">
        <a-spin size="large" />
        <p class="mt-3 text-gray-500">数据加载中，请稍候...</p>
      </div>
    </div>

    <!-- 列表内容区 -->
    <div v-else class="content-container">
      <!-- Ant Design风格表格 -->
      <a-table
        :row-key="(item) => item.id"
        :data-source="list"
        :pagination="pagination"
        :loading="loading"
        bordered
        class="shadow-sm rounded-lg overflow-hidden"
        @change="handleTableChange"
      >
        <a-table-column title="序号" width="80">
          <template #default="{ index }">
            {{ (pagination.current - 1) * pagination.pageSize + index + 1 }}
          </template>
        </a-table-column>

    <!-- 直接展示图片列（移除判断逻辑） -->
    <a-table-column title="资源" width="120">
      <template #default="{ record }">
        <template v-if="record.url.match(/\.(mp4|webm|ogg)$/i)">
          <video
            :src="record.url"
            class="w-16 h-10 object-cover rounded-md"
            controls
          />
        </template>
        <template v-else>
          <img
            :src="record.url + '!s200'"
            alt="资源"
            class="w-16 h-10 object-cover rounded-md"
          />
        </template>
      </template>
    </a-table-column>
        <a-table-column title="排序" dataIndex="sort" width="120" />
        <a-table-column title="操作" width="160">
          <template #default="{ record }">
            <div class="flex space-x-2">
              <a-button type="link" size="small" @click="handleEdit(record)">
                <i class="fa fa-edit mr-1"></i>编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                @click="handleDelete(record.id)"
              >
                <i class="fa fa-edit mr-1"></i>删除
              </a-button>
            </div>
          </template>
        </a-table-column>
      </a-table>

      <!-- 空状态 -->
      <div
        v-if="list.length === 0"
        class="empty-state flex flex-col items-center justify-center py-20"
      >
       
        <p class="text-gray-500 text-lg">暂无数据</p>
        <a-button type="primary" class="mt-4" @click="refreshData">
          <i class="fa fa-refresh mr-2"></i>刷新数据
        </a-button>
      </div>
    </div>

    <!-- 编辑/新增模态框 -->
    <a-modal
      :open="modalVisible"
      :title="formData.id ? '编辑数据' : '新增数据'"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirm-loading="modalLoading"
    >
      <a-form :model="formData" ref="formRef" class="space-y-4">
   
        <a-form-item
          label="排序"
          :rules="[{ required: true, message: '请输入排序' }]"
        >
          <a-input v-model:value="formData.sort" placeholder="请输入排序,排序越大 位置越前" />
        </a-form-item>
          <!-- 新增：图片上传区域 -->
         <a-form-item label="图片">
      <a-upload
        :before-upload="handleBeforeUpload"
        :file-list="formData.url ? [{ uid: '-1', name: 'image.jpg', status: 'done', url: formData.url }] : []"
        :show-upload-list="{show: true, showRemoveIcon: true}"
         @remove="handleRemoveFile"
        class="mb-3"
      >
        <a-button type="primary">
          <i class="fa fa-upload mr-1"></i> 选择图片
        </a-button>
      </a-upload>
      
      <div v-if="formData.url" class="mt-2">
        <img
          :src="formData.url + '!s200'"
          alt="预览图"
          class="w-40 h-40 object-cover rounded-md border border-gray-200 shadow-sm"
        />
      </div>
    </a-form-item>





      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { message } from "ant-design-vue";
// import upyun from "upyun"; // 引入又拍云 SDK
// import UpyunImageUpload from '@/components/UpyunImageUpload.vue'; // 引入上传组件


import {
  getMessageWorkListApi,
  createMessageWorkApi,
  updateMessageWorkApi,
  deleteMessageWorkApi,
  getSignatureApi,
} from "~@/api/common/messagework";

// 状态管理
const loading = ref(false);
const list = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ["10", "20", "50", "100"],
});

// 模态框相关
const modalVisible = ref(false);
const modalLoading = ref(false);
const formData = ref({
  id: null,
  url: "",
  yppurl:"",
  sort: "",
});

const handleRemoveFile = () => {
  formData.value.url = ""
}
const formRef = ref(null);

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
    };
    const res = await getMessageWorkListApi(params);
    list.value = res.data.list || [];
    console.log("接口返回的数据:", res.data.list); // 添加日志输出接口返回的数据
    console.log("赋值给list的数据:", list.value); // 添加日志输出赋值给list的数据
    pagination.total = res.data.total || 0;
  } catch (error) {
    console.error("获取数据失败", error);
    message.error("获取数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 表格变化处理
const handleTableChange = (paginationInfo) => {
  pagination.current = paginationInfo.current;
  pagination.pageSize = paginationInfo.pageSize;
  fetchData();
};

// 刷新数据
const refreshData = () => {
  pagination.current = 1;
  fetchData();
};

// 新增和编辑
const handleAdd = () => {
  formData.value = {
    id: null,
    sort: "",
    url: "",
  imageKey: "", // 图片在又拍云的SaveKey
  };
  modalVisible.value = true;
};

const handleEdit = (record) => {
  formData.value = { ...record };
  modalVisible.value = true;
};

const handleModalOk = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    modalLoading.value = true;
    console.log("formRef", formRef.value.url);
        console.log("formRef", formRef.value.sort);
    if (formData.value.id) {
      const editedData = {
        id: parseInt(formData.value.id, 10),
        url: formData.value.url,
        sort: parseInt(formData.value.sort, 10),
      };

      // 更新
      await updateMessageWorkApi(editedData);
      message.success("更新成功");
    } else {
      const addData = {
        url: formData.value.url,
        sort: parseInt(formData.value.sort, 10), // 将sort转换为int类型
      };
      // 新增
      await createMessageWorkApi(addData);
      message.success("新增成功");
    }

    modalVisible.value = false;
    fetchData();
  } catch (error) {
    console.error("表单验证失败", error);
  } finally {
    modalLoading.value = false;
  }
};

const handleModalCancel = () => {
  modalVisible.value = false;
};

// 删除
const handleDelete = async (id) => {
  try {
    console.log("删除失败", id);
    await deleteMessageWorkApi({ id: id });
    message.success("删除成功");
    fetchData();
  } catch (error) {
    console.error("删除失败", error);
    message.error("删除失败，请稍后重试");
  }
};


//又拍云
const uploadFile = ref(null); // 临时存储上传的文件


const handleBeforeUpload = async (file) => {
  try {
    // 获取REST API签名信息
     const fileExt = file.name.split('.').pop() || 'jpg';
    const randomFileName = `image-${Date.now()}.${fileExt}`;
    
    // 传递完整的saveKey路径，使用模板字符串
    const { data } = await getSignatureApi({ 
      save_key: `/uploads/${randomFileName}` // 正确使用模板字符串
    });
    const { policy, authorization, bucket, save_key } = data;
    const expiration = Math.floor(Date.now() / 1000) + 30 * 60;
    // 构建FormData
    const formDatas = new FormData();
    formDatas.append("policy", policy);        // 上传策略
    formDatas.append("authorization", authorization); // 签名
    formDatas.append("file", file);
    formDatas.append("expiration",  expiration.toString());
              // 要上传的文件
    formDatas.append("save-key", save_key);    // 文件保存路径
    
    // 发送请求到又拍云API
    const response = await fetch(`https://v0.api.upyun.com/${bucket}`, {
      method: "POST",
      body: formDatas,
    });

    // 处理响应
    if (response.status != 200) {
      const errorData = await response.json();
      throw new Error(`上传失败 (${errorData.code}): ${errorData.message}`);
    }
    
    const result = await response.json();
    console.log("上传成功:", result.url);
     console.log("处理","dddd")
    // 更新表单数据
    formData.value.url = `//static.aoneandatwodesign.com/${result.url}`;
    console.log("处理",formData.value.url)
    message.success("图片上传成功");
    return false; // 阻止组件默认上传
  } catch (error) {
    console.log("处理",error)
    message.error("上传失败，请重试");
    return false;
  }
};


// 生命周期钩子
onMounted(async () => {
  await fetchData();
//   await fetchupyon();
});
</script>

<style scoped>
.ant-upload {
  width: 100%;
}

.ant-upload .ant-btn {
  width: 100%;
}

.ant-upload-list {
  margin-top: 16px;
}
</style>
