<template>
  <div class="message-index-container">
    <!-- 顶部工具栏 -->
    <div class="toolbar flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">成员</h1>
      <div class="flex space-x-2">
        <a-button type="primary" @click="handleAdd">
          <span class="flex items-center">
            <i class="fa fa-plus mr-2"></i>新增
          </span>
        </a-button>
        <a-button @click="refreshData">
          <span class="flex items-center">
            <i class="fa fa-refresh mr-2"></i>刷新
          </span>
        </a-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-mask flex items-center justify-center h-[300px]">
      <div class="flex flex-col items-center">
        <a-spin size="large" />
        <p class="mt-3 text-gray-500">数据加载中，请稍候...</p>
      </div>
    </div>

    <!-- 列表内容区 -->
    <div v-else class="content-container">
      <a-table
        :row-key="(item) => item.id"
        :data-source="list"
        :pagination="pagination"
        :loading="loading"
        bordered
        class="shadow-sm rounded-lg overflow-hidden"
        @change="handleTableChange"
      >
        <a-table-column title="序号" width="80">
          <template #default="{ index }">
            {{ (pagination.current - 1) * pagination.pageSize + index + 1 }}
          </template>
        </a-table-column>

        <a-table-column title="头像" width="120">
          <template #default="{ record }">
            <img
              :src="record.avatar + '!s200'"
              alt="头像"
              class="w-16 h-16 object-cover rounded-full"
            />
          </template>
        </a-table-column>

        <a-table-column title="英文简介" width="300">
          <template #default="{ record }">
            <div>{{ record.bio_en }}</div>
          </template>
        </a-table-column>

        <a-table-column title="中文简介" width="300">
          <template #default="{ record }">
            <div>{{ record.bio_cn }}</div>
          </template>
        </a-table-column>

        <a-table-column title="操作" width="160">
          <template #default="{ record }">
            <div class="flex space-x-2">
              <a-button type="link" size="small" @click="handleEdit(record)">
                <i class="fa fa-edit mr-1"></i>编辑
              </a-button>
              <a-button type="link" size="small" @click="handleDelete(record.id)">
                <i class="fa fa-trash mr-1"></i>删除
              </a-button>
            </div>
          </template>
        </a-table-column>
      </a-table>

      <!-- 空状态 -->
      <div v-if="list.length === 0" class="empty-state flex flex-col items-center justify-center py-20">
        <p class="text-gray-500 text-lg">暂无数据</p>
        <a-button type="primary" class="mt-4" @click="refreshData">
          <i class="fa fa-refresh mr-2"></i>刷新数据
        </a-button>
      </div>
    </div>

    <!-- 编辑/新增模态框 -->
    <a-modal
      :open="modalVisible"
      :title="formData.id ? '编辑成员' : '新增成员'"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirm-loading="modalLoading"
      width="800px"
    >
      <a-form :model="formData" ref="formRef" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="头像" :rules="[{ required: true, message: '请上传头像' }]">
          <a-upload
            :before-upload="handleBeforeUpload"
            :file-list="formData.avatar ? [{ uid: '-1', name: 'avatar.jpg', status: 'done', url: formData.avatar}] : []"
            :show-upload-list="{show: true, showRemoveIcon: true}"
               @remove="handleRemoveFile"
          >
            <a-button type="primary">
              <i class="fa fa-upload mr-1"></i>选择图片
            </a-button>
          </a-upload>
          
          <div v-if="formData.avatar" class="mt-2">
            <img
              :src="formData.avatar + '!s200'"
              alt="预览图"
              class="w-32 h-32 object-cover rounded-full border border-gray-200"
            />
          </div>
        </a-form-item>

        <a-form-item label="英文简介">
          <a-textarea v-model:value="formData.bio_en" placeholder="请输入英文简介" :rows="4" />
        </a-form-item>

        <a-form-item label="中文简介">
          <a-textarea v-model:value="formData.bio_cn" placeholder="请输入中文简介" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { message } from "ant-design-vue";
import {
  createMemberApi,
  updateMemberApi,
  deleteMemberApi,
  getMemberListApi,
} from "~@/api/common/member";
import { getSignatureApi } from "~@/api/common/messagework";
const handleRemoveFile = () => {
  formData.value.avatar = ""
}
// 状态管理
const loading = ref(false);
const list = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ["10", "20", "50", "100"],
});

// 模态框相关
const modalVisible = ref(false);
const modalLoading = ref(false);
const formData = ref({
  id: null,
  avatar: "",
  bio_en: "",
  bio_cn: "",
});
const formRef = ref(null);

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
    };
    const res = await getMemberListApi(params);
    list.value = res.data.list || [];
    pagination.total = res.data.total || 0;
  } catch (error) {
    console.error("获取数据失败", error);
    message.error("获取数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 表格变化处理
const handleTableChange = (paginationInfo) => {
  pagination.current = paginationInfo.current;
  pagination.pageSize = paginationInfo.pageSize;
  fetchData();
};

// 刷新数据
const refreshData = () => {
  pagination.current = 1;
  fetchData();
};

// 新增和编辑
const handleAdd = () => {
  formData.value = {
    id: null,
    avatar: "",
    bio_en: "",
    bio_cn: "",
  };
  modalVisible.value = true;
};

const handleEdit = (record) => {
  formData.value = { ...record };
  modalVisible.value = true;
};

const handleModalOk = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    modalLoading.value = true;

    const submitData = {
      avatar: formData.value.avatar,
      bio_en: formData.value.bio_en,
      bio_cn: formData.value.bio_cn,
    };

    if (formData.value.id) {
      await updateMemberApi({
        id: parseInt(formData.value.id, 10),
        ...submitData
      });
      message.success("更新成功");
    } else {
      await createMemberApi(submitData);
      message.success("新增成功");
    }

    modalVisible.value = false;
    fetchData();
  } catch (error) {
    console.error("表单验证失败", error);
  } finally {
    modalLoading.value = false;
  }
};

const handleModalCancel = () => {
  modalVisible.value = false;
};

// 删除
const handleDelete = async (id) => {
  try {
    await deleteMemberApi({ id: id });
    message.success("删除成功");
    fetchData();
  } catch (error) {
    console.error("删除失败", error);
    message.error("删除失败，请稍后重试");
  }
};

// 图片上传
const handleBeforeUpload = async (file) => {
  try {
    const fileExt = file.name.split('.').pop() || 'jpg';
    const randomFileName = `avatar-${Date.now()}.${fileExt}`;
    
    const { data } = await getSignatureApi({ 
      save_key: `/uploads/${randomFileName}`
    });
    const { policy, authorization, bucket, save_key } = data;
    const expiration = Math.floor(Date.now() / 1000) + 30 * 60;

    const formDatas = new FormData();
    formDatas.append("policy", policy);
    formDatas.append("authorization", authorization);
    formDatas.append("file", file);
    formDatas.append("expiration", expiration.toString());
    formDatas.append("save-key", save_key);
    
    const response = await fetch(`https://v0.api.upyun.com/${bucket}`, {
      method: "POST",
      body: formDatas,
    });

    if (response.status !== 200) {
      const errorData = await response.json();
      throw new Error(`上传失败 (${errorData.code}): ${errorData.message}`);
    }
    
    const result = await response.json();
    formData.value.avatar = `//static.aoneandatwodesign.com/${result.url}`;
    message.success("头像上传成功");
    return false;
  } catch (error) {
    console.error("上传失败", error);
    message.error("上传失败，请重试");
    return false;
  }
};

onMounted(async () => {
  await fetchData();
});
</script>

<style scoped>
.ant-upload {
  width: 100%;
}

.ant-upload .ant-btn {
  width: 100%;
}

.ant-upload-list {
  margin-top: 16px;
}
</style>
