<template>
  <div class="message-index-container">
    <!-- 顶部工具栏 -->
    <div class="toolbar flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">获奖管理</h1>
      <div class="flex space-x-2">
        <a-button type="primary" @click="handleAdd">
          <span class="flex items-center">
            <i class="fa fa-plus mr-2"></i>新增
          </span>
        </a-button>
        <a-button @click="refreshData">
          <span class="flex items-center">
            <i class="fa fa-refresh mr-2"></i>刷新
          </span>
        </a-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="loading-mask flex items-center justify-center h-[300px]"
    >
      <div class="flex flex-col items-center">
        <a-spin size="large" />
        <p class="mt-3 text-gray-500">数据加载中，请稍候...</p>
      </div>
    </div>

    <!-- 列表内容区 -->
    <div v-else class="content-container">
      <!-- Ant Design风格表格 -->
      <a-table
        :row-key="(item) => item.id"
        :data-source="list"
        :pagination="pagination"
        :loading="loading"
        bordered
        class="shadow-sm rounded-lg overflow-hidden"
        @change="handleTableChange"
      >
        <a-table-column title="序号" width="80">
          <template #default="{ index }">
            {{ (pagination.current - 1) * pagination.pageSize + index + 1 }}
          </template>
        </a-table-column>
        <a-table-column title="主题" dataIndex="name" width="200" />
        <a-table-column title="信息" dataIndex="work" width="200" />
        <a-table-column title="年份" dataIndex="year" width="200" />
        <a-table-column title="操作" width="160">
          <template #default="{ record }">
            <div class="flex space-x-2">
              <a-button type="link" size="small" @click="handleEdit(record)">
                <i class="fa fa-edit mr-1"></i>编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                @click="handleDelete(record.id)"
              >
                <i class="fa fa-edit mr-1"></i>删除
              </a-button>
            </div>
          </template>
        </a-table-column>
      </a-table>

      <!-- 空状态 -->
      <div
        v-if="list.length === 0"
        class="empty-state flex flex-col items-center justify-center py-20"
      >
        <p class="text-gray-500 text-lg">暂无数据</p>
        <a-button type="primary" class="mt-4" @click="refreshData">
          <i class="fa fa-refresh mr-2"></i>刷新数据
        </a-button>
      </div>
    </div>

    <!-- 编辑/新增模态框 -->
    <a-modal
      :open="modalVisible"
      :title="formData.id ? '编辑数据' : '新增数据'"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirm-loading="modalLoading"
    >
      <a-form :model="formData" ref="formRef" class="space-y-4">
         <a-form-item label="主题" :rules="[{ required: true, message: '请选择主题' }]">
        <a-select
          v-model:value="formData.name"
          placeholder="请选择主题"
        >
          <a-select-option value="award">award</a-select-option>
          <a-select-option value="interview">interview</a-select-option>
          <a-select-option value="exhibition">exhibition</a-select-option>
        </a-select>
      </a-form-item>
        <a-form-item label="年份">
          <a-input v-model:value="formData.year" placeholder="请输入年份" />
        </a-form-item>
        <a-form-item label="信息">
          <a-input v-model:value="formData.work" placeholder="请输入信息" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { message } from "ant-design-vue";

import {
  createCategoryApi,
  updateCategoryApi,
  getCategoryListApi,
  deleteCategoryApi,
} from "~@/api/common/category";
import {
  createAwardApi,
  getAwardListApi,
  deleteAwardApi,
  updateAwardApi,
} from "~@/api/common/award";
// 状态管理
const loading = ref(false);
const list = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ["10", "20", "50", "100"],
});

// 模态框相关
const modalVisible = ref(false);
const modalLoading = ref(false);
const formData = ref({
  id: null,
  year: null,
  name: "",
  work: "",
});
const formRef = ref(null);

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
    };
    const res = await getAwardListApi(params);
    list.value = res.data.list || [];

    pagination.total = res.data.total || 0;
  } catch (error) {
  } finally {
    loading.value = false;
  }
};

// 表格变化处理
const handleTableChange = (paginationInfo) => {
  pagination.current = paginationInfo.current;
  pagination.pageSize = paginationInfo.pageSize;
  fetchData();
};

// 刷新数据
const refreshData = () => {
  pagination.current = 1;
  fetchData();
};

// 新增和编辑
const handleAdd = () => {
  formData.value = {
    id: null,
    type_name: "",
  };
  modalVisible.value = true;
};

const handleEdit = (record) => {
  formData.value = { ...record };
  modalVisible.value = true;
};

const handleModalOk = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    modalLoading.value = true;

    if (formData.value.id) {
      const editedData = {
        id: parseInt(formData.value.id, 10),
        work: formData.value.work,
         name: formData.value.name,
        year: parseInt(formData.value.year, 10),
      };

      // 更新
      await updateAwardApi(editedData);
      message.success("更新成功");
    } else {
      const addData = {
       work: formData.value.work,
         name: formData.value.name,
        year: parseInt(formData.value.year, 10),
      };
      // 新增
      await createAwardApi(addData);
      message.success("新增成功");
    }

    modalVisible.value = false;
    fetchData();
  } catch (error) {
    console.error("表单验证失败", error);
  } finally {
    modalLoading.value = false;
  }
};

const handleModalCancel = () => {
  modalVisible.value = false;
};

// 删除
const handleDelete = async (id) => {
  try {
    console.log("删除失败", id);
    await deleteAwardApi({ id: id });
    message.success("删除成功");
    fetchData();
  } catch (error) {
    console.error("删除失败", error);
    message.error("删除失败，请稍后重试");
  }
};

// 生命周期钩子
onMounted(async () => {
  await fetchData();
});
</script>

<style scoped></style>
