<template>
  <div class="message-index-container">
    <!-- 顶部工具栏 -->
    <div class="toolbar flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">作品管理</h1>
      <div class="flex space-x-2">
        <a-button type="primary" @click="handleAdd">
          <span class="flex items-center">
            <i class="fa fa-plus mr-2"></i>新增
          </span>
        </a-button>
        <a-button @click="refreshData">
          <span class="flex items-center">
            <i class="fa fa-refresh mr-2"></i>刷新
          </span>
        </a-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-mask flex items-center justify-center h-[300px]">
      <div class="flex flex-col items-center">
        <a-spin size="large" />
        <p class="mt-3 text-gray-500">数据加载中，请稍候...</p>
      </div>
    </div>

    <!-- 列表内容区 -->
    <div v-else class="content-container">
      <!-- Ant Design风格表格 -->
      <a-table :row-key="(item) => item.id" :data-source="list" :pagination="pagination" :loading="loading" bordered
        class="shadow-sm rounded-lg overflow-hidden" @change="handleTableChange">
        <a-table-column title="序号" width="80">
          <template #default="{ index }">
            {{ (pagination.current - 1) * pagination.pageSize + index + 1 }}
          </template>
        </a-table-column>
        <a-table-column title="client" dataIndex="client" width="200" />
        <a-table-column title="project" dataIndex="project" width="200" />
        <a-table-column title="type" dataIndex="type" width="120" />
        <a-table-column title="year" dataIndex="year" width="120" />
        <a-table-column title="排序" dataIndex="sort" width="120" />

        <a-table-column title="操作" width="160">
          <template #default="{ record }">
            <div class="flex space-x-2">
              <a-button type="link" size="small" @click="handleEdit(record)">
                <i class="fa fa-edit mr-1"></i>编辑
              </a-button>
              <a-button type="link" size="small" @click="handleDetail(record)">
                <i class="fa fa-edit mr-1"></i>详情图
              </a-button>
              <a-button type="link" size="small" @click="handleDelete(record.id)">
                <i class="fa fa-edit mr-1"></i>删除
              </a-button>
            </div>
          </template>
        </a-table-column>
      </a-table>

      <!-- 空状态 -->
      <div v-if="list.length === 0" class="empty-state flex flex-col items-center justify-center py-20">
        <p class="text-gray-500 text-lg">暂无数据</p>
        <a-button type="primary" class="mt-4" @click="refreshData">
          <i class="fa fa-refresh mr-2"></i>刷新数据
        </a-button>
      </div>
    </div>

    <!-- 编辑/新增模态框 -->
    <a-modal :open="modalVisible" :title="formData.id ? '编辑数据' : '新增数据'" @ok="handleModalOk" @cancel="handleModalCancel"
      :confirm-loading="modalLoading">
      <a-form :model="formData" ref="formRef" class="space-y-4" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="client">
          <a-input v-model:value="formData.client" placeholder="请输入client" />
        </a-form-item>
        <a-form-item label="project">
          <a-input v-model:value="formData.project" placeholder="请输入project" />
        </a-form-item>
        <a-form-item label="type">
          <a-input v-model:value="formData.type" placeholder="请输入type,多个需要逗号分隔" />
        </a-form-item>

        <a-form-item label="Year">
          <a-input v-model:value="formData.year" placeholder="请输入 Year" />
        </a-form-item>
        <a-form-item label="中文标签">
          <a-input v-model:value="formData.ch_title" placeholder="请输入中文标签" />
        </a-form-item>
        <a-form-item label="英文标签">
          <a-input v-model:value="formData.en_title" placeholder="请输入英文标签" />
        </a-form-item>
        <a-form-item label="详情">
          <a-textarea v-model:value="formData.content" placeholder="请输入简介" :rows="4" />
        </a-form-item>
        <a-form-item label="简介">
          <a-textarea v-model:value="formData.introduction" placeholder="请输入简介" :rows="4" />
        </a-form-item>
        <a-form-item label="排序">
          <a-input v-model:value="formData.sort" placeholder="请输入排序" />
        </a-form-item>

        <!-- 新增：图片上传区域 -->
        <a-form-item label="图片">
          <a-upload :before-upload="handleBeforeUpload" :file-list="formData.img
            ? [
              {
                uid: '-1',
                name: 'image.jpg',
                status: 'done',
                url: formData.img,
              },
            ]
            : []
            " :show-upload-list="{ show: true, showRemoveIcon: true }" @remove="handleRemoveImage" class="mb-3">
            <a-button type="primary">
              <i class="fa fa-upload mr-1"></i> 选择图片
            </a-button>
          </a-upload>

          <div v-if="formData.img" class="mt-2">
            <img :src="formData.img + '!s200'" alt="预览图"
              class="w-40 h-40 object-cover rounded-md border border-gray-200 shadow-sm" />
          </div>
        </a-form-item>
      </a-form>
    </a-modal>











    <!-- 详情图弹窗 -->
    <a-modal :open="detailModalVisible" title="作品详情图自由布局" @ok="handleDetailSave" @cancel="detailModalVisible = false"
      width="90%" :footer="detailFooter" class="detail-modal">
      <DetailImageGridEditor ref="detailGridEditor" :initial-images="bannerConfig.img_arr"
        :upload-function="handleDetailImageUpload" @save="handleGridLayoutSave" @change="handleGridLayoutChange" />
    </a-modal>




























  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { message } from "ant-design-vue";
import {
  getMessageIndexListApi,
  createMessageIndexApi,
  updateMessageIndexApi,
  deleteMessageIndexApi,
  getMessageIndexDetail,
  createOrUpdateMessageIndexDetailApi,
} from "~@/api/common/messageindex";

import { getSignatureApi } from "~@/api/common/messagework";

import { getCategoryListApi } from "~@/api/common/category";
import { debounce } from 'lodash';
import DetailImageGridEditor from '~@/components/detail-image-grid/DetailImageGridEditor.vue';

// 删除已有详情图
const handleDeleteDetailImage = (item) => {
  // 直接从 bannerConfig.img_arr 中移除图片
  bannerConfig.value.img_arr = bannerConfig.value.img_arr.filter(
    (imgItem) => imgItem.img !== item.img
  );
  message.success("图片已删除");
};

// 状态管理
const loading = ref(false);
const list = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ["10", "20", "50", "100"],
});
// 删除逻辑（仅前端清空）
const handleRemoveImage = () => {
  formData.value.img = "";
};
const filter = reactive({
  client: "",
  project: "",
  type: "",
});
//详情图
const bannerConfig = ref({
  index_id: null,
  img_arr: [],
});

// 详情图弹窗状态
const detailModalVisible = ref(false);
const detailGridEditor = ref(null);


// sortedImages computed - 不再需要，由GridStack组件处理
// const sortedImages = computed(() => bannerConfig.value.img_arr);


const handleDetail = async (record) => {
  try {
    const { data } = await getMessageIndexDetail(record.id);
    console.log("data", data.index_id);

    // 直接解构赋值，无论img_arr是否为空
    bannerConfig.value = {
      index_id: data.index_id,
      img_arr: data.img_arr || [],
    };
    detailModalVisible.value = true; // 打开弹窗
    console.log("详情数据赋值成功", bannerConfig.value);
  } catch (error) {
    console.error("请求详情接口失败", error);
    // 静默失败，不显示错误提示
  }
};

// 详情图弹窗专用的上传状态（避免与主表单冲突）
// const detailFileList = ref([]); // 临时存储上传的详情图片 - 不再需要
// const detailImageLoading = ref(false); // 详情图上传加载状态 - 不再需要

// 复用编辑弹窗的 handleBeforeUpload 逻辑，但调整保存路径和数据绑定
// const handleDetailImageUpload = async (file) => {
//   try {
//     // 生成详情图专属文件名（例如添加 `detail-` 前缀）
//     const fileExt = file.name.split(".").pop() || "jpg";
//     const randomFileName = `detail-${Date.now()}.${fileExt}`;

//     // 调用同一签名接口，但指定详情图保存路径（如 `/details/` 目录）
//     const { data } = await getSignatureApi({
//       save_key: `/details/${randomFileName}`, // 与主图路径区分
//     });

//     // 复用编辑弹窗的上传请求逻辑（完全相同）
//     const formDatas = new FormData();
//     formDatas.append("policy", data.policy);
//     formDatas.append("authorization", data.authorization);
//     formDatas.append("file", file);
//     formDatas.append("expiration", Math.floor(Date.now() / 1000) + 30 * 60);
//     formDatas.append("save-key", data.save_key); // 注意参数名是 save-key

//     const response = await fetch(`https://v0.api.upyun.com/${data.bucket}`, {
//       method: "POST",
//       body: formDatas,
//     });
//     if (!response.ok) throw new Error("上传失败");

//     const result = await response.json();
//     const imageUrl = `//static.aoneandatwodesign.com/${result.url}`; // 复用域名拼接

//     // 将上传成功的图片添加到详情图临时列表（需包含 sort 字段，默认 0）
//     detailFileList.value = [
//       ...detailFileList.value,
//       {
//         uid: file.uid,
//         name: file.name,
//         status: "done",
//         url: imageUrl,
//         sort: 0, // 初始排序为 0，后续可通过接口调整
//       },
//     ];
//     message.success("详情图上传成功");
//     return false; // 阻止组件默认上传
//   } catch (error) {
//     message.error("上传失败，请重试");
//     return false;
//   }
// };






// 上传队列 - 不再需要，由GridStack组件处理
// const uploadQueue = ref([]);
// const isUploading = ref(false);

// 修改 handleDetailImageUpload 方法 - 改为返回Promise
const handleDetailImageUpload = async (file) => {
  try {
    // 生成详情图专属文件名
    const fileExt = file.name.split(".").pop() || "jpg";
    const randomFileName = `detail-${Date.now()}.${fileExt}`;

    // 调用签名接口
    const { data } = await getSignatureApi({
      save_key: `/details/${randomFileName}`,
    });

    const formDatas = new FormData();
    formDatas.append("policy", data.policy);
    formDatas.append("authorization", data.authorization);
    formDatas.append("file", file);
    formDatas.append("expiration", Math.floor(Date.now() / 1000) + 30 * 60);
    formDatas.append("save-key", data.save_key);

    const response = await fetch(`https://v0.api.upyun.com/${data.bucket}`, {
      method: "POST",
      body: formDatas,
    });

    if (!response.ok) throw new Error("上传失败");

    const result = await response.json();
    const imageUrl = `//static.aoneandatwodesign.com/${result.url}`;

    return {
      url: imageUrl,
      name: file.name
    };
  } catch (error) {
    console.error("上传失败:", error);
    throw error;
  }
};






// 详情图删除逻辑 - 不再需要，由GridStack组件处理
// const handleDetailRemoveImage = (file) => {
//   detailFileList.value = detailFileList.value.filter(
//     (item) => item.uid !== file.uid
//   );
// };



// 处理栅格布局保存
const handleGridLayoutSave = (layout) => {
  console.log("栅格布局保存:", layout);
  // 这里可以处理布局数据的额外保存逻辑
};

// 处理栅格布局变化
const handleGridLayoutChange = (layout) => {
  console.log("栅格布局变化:", layout);
  // 这里可以处理布局变化的实时保存
};

const handleDetailSave = async () => {
  if (!bannerConfig.value.index_id) {
    message.error("缺少作品 ID，无法保存");
    return;
  }

  try {
    // 从栅格编辑器获取布局数据
    const layoutData = detailGridEditor.value?.getLayoutData() || [];

    // 调用接口提交详情图数据（包含布局信息）
    await createOrUpdateMessageIndexDetailApi({
      index_id: bannerConfig.value.index_id,
      img_arr: layoutData,
    });

    message.success("详情图布局保存成功");
    detailModalVisible.value = false;
    fetchData(); // 刷新主列表数据
  } catch (error) {
    console.error("保存详情图失败", error);
    message.error("保存失败，请重试");
  }
};





// 模态框相关
const modalVisible = ref(false);
const modalLoading = ref(false);
const formData = ref({
  id: null,
  client: "",
  project: "",
  type: "",
  year: "",
  ch_title: "",
  en_title: "",
  path: "",
  introduction: "",
  content: "",
  img: "",
  sort: null
});
const formRef = ref(null);

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filter,
    };
    const res = await getMessageIndexListApi(params);
    list.value = res.data.list || [];
    console.log("接口返回的数据:", res.data.list); // 添加日志输出接口返回的数据
    console.log("赋值给list的数据:", list.value); // 添加日志输出赋值给list的数据
    pagination.total = res.data.total || 0;
  } catch (error) {
    console.error("获取数据失败", error);
    message.error("获取数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 表格变化处理
const handleTableChange = (paginationInfo) => {
  pagination.current = paginationInfo.current;
  pagination.pageSize = paginationInfo.pageSize;
  fetchData();
};

// 搜索和重置
const searchData = () => {
  pagination.current = 1;
  fetchData();
};

//又拍云
const uploadFile = ref(null); // 临时存储上传的文件

const handleBeforeUpload = async (file) => {
  try {
    // 获取REST API签名信息
    const fileExt = file.name.split(".").pop() || "jpg";
    const randomFileName = `image-${Date.now()}.${fileExt}`;

    // 传递完整的saveKey路径，使用模板字符串
    const { data } = await getSignatureApi({
      save_key: `/uploads/${randomFileName}`, // 正确使用模板字符串
    });
    const { policy, authorization, bucket, save_key } = data;
    const expiration = Math.floor(Date.now() / 1000) + 30 * 60;
    // 构建FormData
    const formDatas = new FormData();
    formDatas.append("policy", policy); // 上传策略
    formDatas.append("authorization", authorization); // 签名
    formDatas.append("file", file);
    formDatas.append("expiration", expiration.toString());
    // 要上传的文件
    formDatas.append("save-key", save_key); // 文件保存路径

    // 发送请求到又拍云API
    const response = await fetch(`https://v0.api.upyun.com/${bucket}`, {
      method: "POST",
      body: formDatas,
    });

    // 处理响应
    if (response.status != 200) {
      const errorData = await response.json();
      throw new Error(`上传失败 (${errorData.code}): ${errorData.message}`);
    }

    const result = await response.json();
    console.log("上传成功:", result.url);
    console.log("处理", "dddd");
    // 更新表单数据
    formData.value.img = `//static.aoneandatwodesign.com/${result.url}`;
    console.log("处理", formData.value.img);
    message.success("图片上传成功");
    return false; // 阻止组件默认上传
  } catch (error) {
    console.log("处理", error);
    message.error("上传失败，请重试");
    return false;
  }
};

const resetFilter = () => {
  filter.client = "";
  filter.project = "";
  filter.type = "";
  searchData();
};

// 刷新数据
const refreshData = () => {
  pagination.current = 1;
  fetchData();
};

// 新增和编辑
const handleAdd = () => {
  formData.value = {
    id: null,
    client: "",
    project: "",
    type: "",
    year: "",
    path: "",
    ch_title: "",
    en_title: "",
    introduction: "",
    content: "",
    img: "",
  };
  modalVisible.value = true;
};

const handleEdit = (record) => {
  formData.value = { ...record };
  modalVisible.value = true;
};
//上传详情图

const categoryList = ref([]); // 存储分类列表
const categoryLoading = ref(false); // 分类数据加载状态
const fetchCategoryList = async () => {
  categoryLoading.value = true;
  try {
    const res = await getCategoryListApi({ page: 1, pageSize: 100 });
    // 映射接口数据为 { value, label } 格式
    categoryList.value = (res.data?.list || []).map((item) => ({
      value: item.id, // 存储id作为value
      label: item.type_name, // 显示type_name作为标签
    }));
    console.log("处理后的分类数据：", categoryList.value); // 新增日志
  } catch (error) {
    console.error("获取分类数据失败", error);
    message.error("分类数据加载失败，请稍后重试");
  } finally {
    categoryLoading.value = false;
  }
};

const handleModalOk = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    modalLoading.value = true;
    console.log("formRef", formRef.value);
    if (formData.value.id) {
      const editedData = {
        id: parseInt(formData.value.id, 10),
        client: formData.value.client,
        project: formData.value.project,
        type: formData.value.type,
        year: parseInt(formData.value.year, 10),
        ch_title: formData.value.ch_title,
        en_title: formData.value.en_title,
        path: formData.value.path,
        content: formData.value.content,
        introduction: formData.value.introduction,
        img: formData.value.img,
        sort: parseInt(formData.value.sort, 10),
      };

      // 更新
      await updateMessageIndexApi(editedData);
      message.success("更新成功");
    } else {
      const addData = {
        client: formData.value.client,
        project: formData.value.project,
        type: formData.value.type,
        year: parseInt(formData.value.year, 10), // 将year转换为int类型
        ch_title: formData.value.ch_title,
        en_title: formData.value.en_title,
        path: formData.value.path,
        content: formData.value.content,
        introduction: formData.value.introduction,
        img: formData.value.img,
        sort: parseInt(formData.value.sort, 10),
      };
      // 新增
      await createMessageIndexApi(addData);
      message.success("新增成功");
    }

    modalVisible.value = false;
    fetchData();
  } catch (error) {
    console.error("表单验证失败", error);
  } finally {
    modalLoading.value = false;
  }
};

const handleModalCancel = () => {
  modalVisible.value = false;
};

// 删除
const handleDelete = async (id) => {
  try {
    console.log("删除失败", id);
    // const deleteData = {
    //   id:id
    // }
    await deleteMessageIndexApi({ id: id });
    message.success("删除成功");
    fetchData();
  } catch (error) {
    console.error("删除失败", error);
    message.error("删除失败，请稍后重试");
  }
};

// 删除已有图片 - 不再需要，由GridStack组件处理
// const handleExistingImageRemove = (image) => {
//   // 从 bannerConfig 中移除指定图片
//   bannerConfig.value.img_arr = bannerConfig.value.img_arr.filter(
//     (item) => item.img !== image.img
//   );
//   message.success('图片已移除');
// };

// 删除已有详情图 - 不再需要，由GridStack组件处理
// const handleDeleteDetailImage = (item) => {
//   // 直接从 bannerConfig.img_arr 中移除图片
//   bannerConfig.value.img_arr = bannerConfig.value.img_arr.filter(
//     (imgItem) => imgItem.img !== item.img
//   );
//   message.success("图片已删除");
// };

// 定义详情图弹窗的footer
const detailFooter = [
  {
    key: 'cancel',
    text: '取消',
    onClick: () => {
      detailModalVisible.value = false;
    }
  },
  {
    key: 'ok',
    text: '保存布局',
    type: 'primary',
    onClick: handleDetailSave
  }
];


// 生命周期钩子
onMounted(async () => {
  await fetchData();
  await fetchCategoryList(); // 新增分类数据加载
});
</script>

<style scoped></style>
