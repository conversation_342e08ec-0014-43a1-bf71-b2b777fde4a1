<script setup>
defineOptions({
  name: 'BasicForm',
})

import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { getSignatureApi } from "~@/api/common/messagework";
import { getAboutDetailApi, updateAboutApi, createAboutApi } from "~@/api/common/about"
import { UpOutlined, DownOutlined } from '@ant-design/icons-vue';


// 响应式数据
const formState = reactive({
  id: null,
  ch_title: '',    // 中文标题
  en_title: '',    // 英文标题
  email: '',       // 邮箱
  address: '',     // 地址
  follow_us: [],   // 关注我们列表
  img: '',         // 图片URL
});

// 关注我们项目列表
const followUsItems = reactive([
  { name: '', link: '', sort: 0 }
]);

// 新增关注我们项目
const addFollowUsItem = () => {
  followUsItems.push({ name: '', link: '', sort: followUsItems.length });
};

// 删除关注我们项目
const removeFollowUsItem = (index) => {
  followUsItems.splice(index, 1);
  // 重新排序
  followUsItems.forEach((item, idx) => {
    item.sort = idx;
  });
};

// 上移项目
const moveUpItem = (index) => {
  if (index > 0) {
    const temp = followUsItems[index];
    followUsItems[index] = followUsItems[index - 1];
    followUsItems[index - 1] = temp;
    // 更新排序值
    followUsItems.forEach((item, idx) => {
      item.sort = idx;
    });
  }
};

// 下移项目
const moveDownItem = (index) => {
  if (index < followUsItems.length - 1) {
    const temp = followUsItems[index];
    followUsItems[index] = followUsItems[index + 1];
    followUsItems[index + 1] = temp;
    // 更新排序值
    followUsItems.forEach((item, idx) => {
      item.sort = idx;
    });
  }
};

// 页面加载时获取数据
onMounted(() => {
  getAboutDetailApi()
    .then(response => {
      if (response.code === 0 && response.data) {
        const { follow_us, ...rest } = response.data;
        Object.assign(formState, rest);
        
        // 处理关注我们数据
        if (Array.isArray(follow_us) && follow_us.length > 0) {
          // 按sort字段排序
          const sortedFollowUs = [...follow_us].sort((a, b) => a.sort - b.sort);
          followUsItems.splice(0, followUsItems.length, ...sortedFollowUs);
        }
      }
    })
    .catch(error => {
      console.error('获取数据失败:', error);
    });
});

const handleRemoveFile = () => {
  formState.img = '';
};

const formRef = ref();
const uploading = ref(false);

async function handleSubmit() {
  try {
    const values = await formRef.value?.validateFields();
    
    // 整理提交数据
    const submitData = {
      ...values,
      follow_us: followUsItems
    };

    if (formState.id) {
      await updateAboutApi({
        ...submitData,
        id: formState.id
      });
      message.success('更新成功');
    } else {
      await createAboutApi(submitData);
      message.success('创建成功');
    }
  } catch (error) {
    console.log('提交失败:', error);
    message.error('提交失败，请检查表单信息');
  }
}

const handleBeforeUpload = async (file) => {
  try {
    uploading.value = true;
    const fileExt = file.name.split('.').pop() || 'jpg';
    const randomFileName = `image-${Date.now()}.${fileExt}`;
    
    const { data } = await getSignatureApi({ 
      save_key: `/uploads/${randomFileName}`
    });
    const { policy, authorization, bucket, save_key } = data;
    const expiration = Math.floor(Date.now() / 1000) + 30 * 60;

    const formDatas = new FormData();
    formDatas.append("policy", policy);
    formDatas.append("authorization", authorization);
    formDatas.append("file", file);
    formDatas.append("expiration", expiration.toString());
    formDatas.append("save-key", save_key);
    
    const response = await fetch(`https://v0.api.upyun.com/${bucket}`, {
      method: "POST",
      body: formDatas,
    });

    if (response.status !== 200) {
      const errorData = await response.json();
      throw new Error(`上传失败 (${errorData.code}): ${errorData.message}`);
    }
    
    const result = await response.json();
    formState.img = `//static.aoneandatwodesign.com/${result.url}`;
    message.success("图片上传成功");
    return false;
  } catch (error) {
    message.error("上传失败，请重试");
    return false;
  } finally {
    uploading.value = false;
  }
};
</script>

<template>
  <page-container>
    <a-card :body-style="{ padding: '24px 32px' }" :bordered="false">
      <a-form ref="formRef" :model="formState">
        <a-form-item
          name="ch_title"
          label="中文标题"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :rules="[{ required: true, message: '请填写中文标题' }]"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-input
            v-model:value="formState.ch_title"
            placeholder="请输入中文标题"
          />
        </a-form-item>

        <a-form-item
          name="en_title"
          label="英文标题"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :rules="[{ required: true, message: '请填写英文标题' }]"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-input
            v-model:value="formState.en_title"
            placeholder="请输入英文标题"
          />
        </a-form-item>

        <a-form-item
          name="email"
          label="邮箱"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :rules="[{
            required: true,
            type: 'email',
            pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
            message: '请输入正确的邮箱格式'
          }]"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-input
            v-model:value="formState.email"
            placeholder="请输入邮箱"
            type="email"
          />
        </a-form-item>

        <a-form-item
          name="address"
          label="地址"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :rules="[{ required: true, message: '请填写地址' }]"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-textarea
            v-model:value="formState.address"
            :rows="4"
            placeholder="请输入地址"
          />
        </a-form-item>

        <!-- 关注我们（动态表单） -->
        <div class="follow-us-container">
          <div class="follow-us-header">
            <span class="follow-us-title">关注我们</span>
            <a-button type="link" @click="addFollowUsItem">添加项目</a-button>
          </div>
          
          <div v-for="(item, index) in followUsItems" :key="index" class="follow-us-item">
            <div class="follow-us-sort-buttons" style="z-index: 1;">
              <a-button 
                type="link" 
                :disabled="index === 0"
                @click="moveUpItem(index)"
              >
                <up-outlined />
              </a-button>
              <a-button 
                type="link" 
                :disabled="index === followUsItems.length - 1"
                @click="moveDownItem(index)"
              >
                <down-outlined />
              </a-button>
            </div>
            
            <a-form-item
              :name="['follow_us', index, 'name']"
              label="名称"
              style="margin-top: 24px;"
            >
              <a-input v-model:value="item.name" placeholder="请输入名称" />
            </a-form-item>
            
            <a-form-item
              :name="['follow_us', index, 'link']"
              label="链接"
            >
              <a-input v-model:value="item.link" placeholder="请输入链接" />
            </a-form-item>
            
            <a-button type="link" danger @click="removeFollowUsItem(index)">删除</a-button>
          </div>
        </div>

        <a-form-item
          name="img"
          label="文件"
          :label-col="{ lg: { span: 7 }, sm: { span: 7 } }"
          :wrapper-col="{ lg: { span: 10 }, sm: { span: 17 } }"
        >
          <a-upload
            :before-upload="handleBeforeUpload"
            :file-list="formState.img ? [{ uid: '-1', name: 'image.jpg', status: 'done', url: formState.img }] : []"
            :show-upload-list="{show: true, showRemoveIcon: true}"
            @remove="handleRemoveFile"
            :loading="uploading"
          >
            <a-button type="primary">
              <i class="fa fa-upload mr-1"></i> 选择文件
            </a-button>
          </a-upload>
          
          <div v-if="formState.img" class="mt-3">
            <img
              v-if="formState.img.toLowerCase().match(/\.(jpg|jpeg|png|gif|webp)$/)"
              :src="formState.img + '!s200'"
              alt="预览图"
              class="w-40 h-40 object-cover rounded-md border border-gray-200 shadow-sm"
            />
            <video
              v-else-if="formState.img.toLowerCase().match(/\.(mp4|webm|ogg)$/)"
              :src="formState.img"
              controls
              class="w-40 h-40 object-cover rounded-md border border-gray-200 shadow-sm"
            />
          </div>
        </a-form-item>

        <a-form-item :wrapper-col="{ span: 24 }" style="text-align: center">
          <a-button type="primary" @click="handleSubmit">
            提交表单
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </page-container>
</template>

<style scoped>
.ant-upload {
  width: 100%;
}

.ant-upload .ant-btn {
  width: 100%;
}

.ant-upload-list {
  margin-top: 16px;
}

.follow-us-container {
  margin-bottom: 24px;
}

.follow-us-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.follow-us-title {
  font-size: 16px;
  font-weight: 500;
}

.follow-us-item {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 16px;
  position: relative;
}

.follow-us-sort-buttons {
  position: absolute;
  right: 16px;
  top: 16px;
  display: flex;
  gap: 4px;
}

.follow-us-sort-buttons .ant-btn {
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
}
</style>