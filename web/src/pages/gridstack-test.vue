<template>
  <div class="gridstack-test p-4">
    <h1>GridStack 简单测试</h1>
    
    <div class="mb-4">
      <a-button @click="addSimpleWidget" type="primary">添加简单Widget</a-button>
      <a-button @click="addImageWidget" type="default" class="ml-2">添加图片Widget</a-button>
      <a-button @click="clearAll" danger class="ml-2">清空</a-button>
    </div>
    
    <div class="grid-container border border-gray-300 rounded" style="height: 500px;">
      <div ref="gridContainer" class="grid-stack h-full"></div>
    </div>
    
    <div class="mt-4">
      <h3>调试信息:</h3>
      <pre class="bg-gray-100 p-2 rounded">{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { GridStack } from 'gridstack'
import 'gridstack/dist/gridstack.min.css'

const gridContainer = ref(null)
const gridStack = ref(null)
const debugInfo = ref('等待初始化...')

let widgetCount = 0

const addSimpleWidget = () => {
  if (!gridStack.value) {
    message.error('GridStack未初始化')
    return
  }
  
  try {
    widgetCount++
    const widget = {
      w: 3,
      h: 2,
      content: `<div style="background: #f0f0f0; height: 100%; display: flex; align-items: center; justify-content: center; border-radius: 4px;">Widget ${widgetCount}</div>`
    }
    
    console.log('添加简单widget:', widget)
    const result = gridStack.value.addWidget(widget)
    console.log('添加结果:', result)
    
    if (result) {
      message.success(`Widget ${widgetCount} 添加成功`)
      debugInfo.value = `成功添加 Widget ${widgetCount}`
    } else {
      message.error('添加失败')
      debugInfo.value = '添加失败：返回空值'
    }
  } catch (error) {
    console.error('添加widget失败:', error)
    message.error('添加失败: ' + error.message)
    debugInfo.value = `添加失败: ${error.message}`
  }
}

const addImageWidget = () => {
  if (!gridStack.value) {
    message.error('GridStack未初始化')
    return
  }
  
  try {
    widgetCount++
    const widget = {
      w: 4,
      h: 3,
      content: `<div style="height: 100%; overflow: hidden; border-radius: 4px;">
        <img src="https://picsum.photos/200/150?random=${widgetCount}" 
             style="width: 100%; height: 100%; object-fit: cover;" 
             alt="测试图片 ${widgetCount}" />
      </div>`
    }
    
    console.log('添加图片widget:', widget)
    const result = gridStack.value.addWidget(widget)
    console.log('添加结果:', result)
    
    if (result) {
      message.success(`图片 Widget ${widgetCount} 添加成功`)
      debugInfo.value = `成功添加图片 Widget ${widgetCount}`
    } else {
      message.error('添加失败')
      debugInfo.value = '添加失败：返回空值'
    }
  } catch (error) {
    console.error('添加图片widget失败:', error)
    message.error('添加失败: ' + error.message)
    debugInfo.value = `添加失败: ${error.message}`
  }
}

const clearAll = () => {
  if (gridStack.value) {
    gridStack.value.removeAll()
    widgetCount = 0
    message.success('已清空所有widget')
    debugInfo.value = '已清空所有widget'
  }
}

onMounted(async () => {
  await nextTick()
  
  if (gridContainer.value) {
    try {
      console.log('开始初始化GridStack...')
      console.log('GridStack版本:', GridStack.version || 'unknown')
      
      // 使用最简单的配置
      gridStack.value = GridStack.init({
        column: 24,
        cellHeight: 60,
        margin: 5
      }, gridContainer.value)
      
      console.log('GridStack初始化完成:', !!gridStack.value)
      
      if (gridStack.value) {
        debugInfo.value = 'GridStack初始化成功!'
        message.success('GridStack初始化成功')
        
        // 监听事件
        gridStack.value.on('added', (event, items) => {
          console.log('added事件:', event, items)
        })
        
        gridStack.value.on('change', (event, items) => {
          console.log('change事件:', event, items)
        })
      } else {
        throw new Error('GridStack初始化返回空值')
      }
      
    } catch (error) {
      console.error('GridStack初始化失败:', error)
      debugInfo.value = `初始化失败: ${error.message}`
      message.error('GridStack初始化失败')
    }
  } else {
    console.error('gridContainer.value为空')
    debugInfo.value = 'gridContainer为空'
  }
})
</script>

<style scoped>
.gridstack-test {
  max-width: 1200px;
  margin: 0 auto;
}

.grid-container {
  background: #f8f9fa;
}

.grid-stack {
  background: linear-gradient(90deg, #f0f0f0 1px, transparent 1px),
    linear-gradient(#f0f0f0 1px, transparent 1px);
  background-size: 20px 20px;
}

:deep(.grid-stack-item-content) {
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
