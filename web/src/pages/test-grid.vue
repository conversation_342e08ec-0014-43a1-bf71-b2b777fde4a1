<template>
  <div class="test-grid-page">
    <h1>测试详情图自由布局</h1>
    <a-button @click="openModal" type="primary">打开详情图编辑器</a-button>

    <a-modal :open="modalVisible" title="详情图自由布局测试" @cancel="modalVisible = false" width="90%" :footer="null">
      <DetailImageGridEditor ref="gridEditor" :initial-images="testImages" :upload-function="mockUpload"
        @save="handleSave" @change="handleChange" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import DetailImageGridEditor from '~@/components/detail-image-grid/DetailImageGridEditor.vue'

const modalVisible = ref(false)
const gridEditor = ref(null)

// 测试数据
const testImages = ref([
  {
    img: 'https://picsum.photos/400/300?random=1',
    sort: 1
  },
  {
    img: 'https://picsum.photos/400/300?random=2',
    sort: 2
  }
])

const openModal = () => {
  modalVisible.value = true
}

const mockUpload = async (file) => {
  console.log('模拟上传开始:', file)
  // 模拟上传
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        const result = {
          url: `https://picsum.photos/400/300?random=${Date.now()}`,
          name: file.name
        }
        console.log('模拟上传完成:', result)
        resolve(result)
      } catch (error) {
        console.error('模拟上传失败:', error)
        reject(error)
      }
    }, 1000)
  })
}

const handleSave = (layout) => {
  console.log('保存布局:', layout)
  message.success('布局保存成功')
}

const handleChange = (layout) => {
  console.log('布局变化:', layout)
}
</script>

<style scoped>
.test-grid-page {
  padding: 20px;
}
</style>
