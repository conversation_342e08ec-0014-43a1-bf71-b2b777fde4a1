export default {
  'navBar.lang': 'Languages',
  'layout.user.link.help': 'Help',
  'layout.user.link.privacy': 'Privacy',
  'layout.user.link.terms': 'Terms',
  'app.copyright.produced': 'Produced by Ant Financial Experience Department',
  'app.preview.down.block': 'Download this page to your local project',
  'app.welcome.link.fetch-blocks': 'Get all block',
  'app.welcome.link.block-list': 'Quickly build standard, pages based on `block` development',
  // SettingDrawer
  'app.setting.pagestyle': 'Page style setting',
  'app.setting.pagestyle.dark': 'Dark style',
  'app.setting.pagestyle.light': 'Light style',
  'app.setting.pagestyle.inverted': 'Inverted style',
  'app.setting.pagestyle.mode': 'Layout mode',
  'app.setting.pagestyle.top': 'Top layout mode',
  'app.setting.pagestyle.side': 'Side layout mode',
  'app.setting.pagestyle.mix': 'Mix layout mode',
  'app.setting.content-width.contentWidth': 'Content Width',
  'app.setting.content-width.fixed': 'Fixed',
  'app.setting.content-width.fluid': 'Fluid',
  'app.setting.content-width.fixedHeader': 'Fixed Header',
  'app.setting.content-width.fixSiderbar': 'Fixed Siderbar',
  'app.setting.content-width.splitMenus': 'Auto Split Menus',
  'app.setting.content-width.keepAlive': 'KeepAlive',
  'app.setting.content-width.accordionMode': 'Menu Accordion Mode',
  'app.setting.content-width.leftCollapsed': 'sideMenu Left',
  'app.setting.content-width.compactAlgorithm': 'Compact Mode',
  'app.setting.content-area.title': 'Content Area',
  'app.setting.content-area.header': 'Header',
  'app.setting.content-area.footer': 'Footer',
  'app.setting.content-area.menu': 'Menu',
  'app.setting.content-area.watermark': 'Watermark',
  'app.setting.content-area.menuHeader': 'Menu Header',
  'app.setting.content-area.multiTab': 'Multi Tab',
  'app.setting.content-area.multiTabFixed': 'Fixed Multi Tab',
  'app.setting.content-area.animationName': 'Animation',
  'app.setting.themecolor': 'Theme Color',
  'app.setting.themecolor.dust': 'Dust Red',
  'app.setting.themecolor.volcano': 'Volcano',
  'app.setting.themecolor.sunset': 'Sunset Orange',
  'app.setting.themecolor.cyan': 'Cyan',
  'app.setting.themecolor.green': 'Polar Green',
  'app.setting.themecolor.daybreak': 'Daybreak Blue',
  'app.setting.themecolor.techBlue': 'Technology (default)',
  'app.setting.themecolor.geekblue': 'Geek Glue',
  'app.setting.themecolor.purple': 'Golden Purple',
  'app.setting.navigationmode': 'Navigation Mode',
  'app.setting.sidemenu': 'Side Menu Layout',
  'app.setting.topmenu': 'Top Menu Layout',
  'app.setting.fixedheader': 'Fixed Header',
  'app.setting.fixedsidebar': 'Fixed Sidebar',
  'app.setting.fixedsidebar.hint': 'Works on Side Menu Layout',
  'app.setting.hideheader': 'Hidden Header when scrolling',
  'app.setting.hideheader.hint': 'Works when Hidden Header is enabled',
  'app.setting.othersettings': 'Other Settings',
  'app.setting.weakmode': 'Weak Mode',
  'app.setting.graymode': 'Gray Mode',
  'app.setting.copy': 'Copy Setting',
  'app.setting.copyinfo': 'copy success，please replace default-setting in config/default-setting.js',
  'app.setting.production.hint': 'Setting panel shows in development environment only, please manually modify',
  'app.multiTab.title': 'Multi Tab',
  'app.multiTab.closeCurrent': 'Close Current',
  'app.multiTab.closeOther': 'Close Other',
  'app.multiTab.closeAll': 'Close All',
  'app.multiTab.refresh': 'Refresh',
  'app.multiTab.closeRight': 'Close Right',
  'app.multiTab.closeLeft': 'Close Left',
  // Menu
  'menu.welcome': 'Welcome',
  'menu.more-blocks': 'More Blocks',
  'menu.home': 'Home',
  'menu.admin': 'Admin',
  'menu.admin.sub-page': 'Sub-Page',
  'menu.login': 'Login',
  'menu.register': 'Register',
  'menu.register-result': 'Register Result',
  'menu.dashboard': 'Dashboard',
  'menu.dashboard.analysis': 'Analysis',


     'menu.dashboard.indexSetting': 'Index Setting',
      'menu.dashboard.workSetting': 'Work Setting',


  'menu.dashboard.monitor': 'Monitor',
  'menu.dashboard.workplace': 'Workplace',
  'menu.exception.403': '403',
  'menu.exception.404': '404',
  'menu.exception.500': '500',
  'menu.form': 'Form',
  'menu.form.basic-form': 'Basic Form',
  'menu.form.step-form': 'Step Form',
  'menu.form.step-form.info': 'Step Form(write transfer information)',
  'menu.form.step-form.confirm': 'Step Form(confirm transfer information)',
  'menu.form.step-form.result': 'Step Form(finished)',
  'menu.form.advanced-form': 'Advanced Form',
  'menu.link': 'Link',
  'menu.link.iframe': 'Ant Design',
  'menu.link.antdv': 'Ant Design Vue',
  'menu.link.external': 'BaiDu',
  'menu.menu': 'Menu',
  'menu.menu.menu1': 'Menu1',
  'menu.menu.menu2': 'Menu2',
  'menu.menu.menu3': 'Menu1-1',
  'menu.menu3.menu1': 'Menu1-1-1',
  'menu.menu3.menu2': 'Menu1-1-2',
  'menu.menu.menu4': 'Menu2-1',
  'menu.menu4.menu1': 'Menu2-1-1',
  'menu.menu4.menu2': 'Menu2-1-2',
  'menu.access': 'Access',
  'menu.access.common': 'Common',
  'menu.access.roles': 'Roles',
  'menu.access.menus': 'Menu',
  'menu.access.api': 'API',
  'menu.access.user': 'User',
  'menu.access.admin': 'Admin',
  'menu.list': 'List',
  'menu.list.table-list': 'Search Table',
  'menu.list.basic-list': 'Basic List',
  'menu.list.consult-table': 'Consult Table',
  'menu.list.crud-table': 'CRUD Table',
  'menu.list.card-list': 'Card List',
  'menu.list.search-list': 'Search List',
  'menu.list.search-list.articles': 'Search List(articles)',
  'menu.list.search-list.projects': 'Search List(projects)',
  'menu.list.search-list.applications': 'Search List(applications)',
  'menu.profile': 'Profile',
  'menu.profile.basic': 'Basic Profile',
  'menu.profile.advanced': 'Advanced Profile',
  'menu.result': 'Result',
  'menu.result.success': 'Success',
  'menu.result.fail': 'Fail',
  'menu.exception': 'Exception',
  'menu.exception.not-permission': '403',
  'menu.exception.not-find': '404',
  'menu.exception.server-error': '500',
  'menu.exception.trigger': 'Trigger',
  'menu.account': 'Account',
  'menu.account.center': 'Account Center',
  'menu.account.settings': 'Account Settings',
  'menu.account.trigger': 'Trigger Error',
  'menu.account.logout': 'Logout',
}
