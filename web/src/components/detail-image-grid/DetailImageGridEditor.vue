<template>
  <div class="detail-image-grid-editor">
    <!-- 工具栏 -->
    <div class="toolbar flex justify-between items-center p-4 bg-white border-b border-gray-200">
      <div class="flex items-center space-x-4">
        <h3 class="text-lg font-medium text-gray-800">详情图自由布局</h3>
        <a-upload multiple :before-upload="handleImageUpload" :show-upload-list="false" class="inline-block">
          <a-button type="primary">
            <i class="fa fa-cloud-upload mr-2"></i>上传图片
          </a-button>
        </a-upload>
      </div>

      <div class="flex items-center space-x-2">
        <a-button @click="clearGrid" danger>
          <i class="fa fa-trash mr-1"></i>清空布局
        </a-button>
        <a-button @click="testAddWidget" type="default">
          <i class="fa fa-test mr-1"></i>测试添加
        </a-button>
        <a-button @click="saveLayout" type="primary">
          <i class="fa fa-save mr-1"></i>保存布局
        </a-button>
      </div>
    </div>

    <!-- 编辑器内容区 -->
    <div class="editor-content flex h-[70vh]">
      <!-- 左侧图片库 -->
      <div class="image-library w-64 bg-gray-50 border-r border-gray-200 p-4 overflow-y-auto">
        <h4 class="text-md font-medium text-gray-700 mb-4">图片库</h4>

        <div v-if="imageLibrary.length === 0" class="text-center text-gray-500 py-8">
          <i class="fa fa-image text-3xl mb-2"></i>
          <p>暂无图片</p>
        </div>

        <!-- 图片列表 -->
        <div class="image-list grid grid-cols-2 gap-3">
          <div v-for="(image, index) in imageLibrary" :key="index"
            class="image-item relative group cursor-pointer border border-gray-200 rounded-lg overflow-hidden hover:border-blue-400 transition-colors"
            draggable="true" @dragstart="handleDragStart($event, image)">
            <img :src="image.url && image.url.includes('!') ? image.url : (image.url || '') + '!s200'" :alt="image.name"
              class="w-full h-20 object-cover" />
            <div
              class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity">
                <a-button type="primary" size="small" @click="addImageToGrid(image)">
                  添加
                </a-button>
              </div>
            </div>
            <div class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <a-button type="primary" danger size="small" @click="removeFromLibrary(index)">
                <i class="fa fa-times"></i>
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间栅格编辑区域 -->
      <div class="grid-container flex-1 p-4 bg-white">
        <div class="grid-wrapper h-full border border-gray-300 rounded-lg overflow-hidden">
          <div ref="gridStackContainer" class="grid-stack h-full"></div>
        </div>
      </div>

      <!-- 右侧配置面板 -->
      <div class="config-panel w-80 bg-gray-50 border-l border-gray-200 p-4 overflow-y-auto">
        <h4 class="text-md font-medium text-gray-700 mb-4">配置面板</h4>

        <!-- 栅格配置 -->
        <a-card title="栅格设置" size="small" class="mb-4">
          <a-form layout="vertical" size="small">
            <a-form-item label="行高">
              <a-input-number v-model:value="gridConfig.cellHeight" :min="20" :max="200" @change="updateGridConfig" />
            </a-form-item>
            <a-form-item label="间距">
              <a-input-number v-model:value="gridConfig.margin" :min="0" :max="20" @change="updateGridConfig" />
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 选中元素配置 -->
        <a-card title="元素配置" size="small" class="mb-4" v-if="selectedItem">
          <a-form layout="vertical" size="small">
            <a-form-item label="宽度">
              <a-input-number v-model:value="selectedItem.w" :min="1" :max="24" @change="updateSelectedItem" />
            </a-form-item>
            <a-form-item label="高度">
              <a-input-number v-model:value="selectedItem.h" :min="1" :max="20" @change="updateSelectedItem" />
            </a-form-item>
            <a-form-item label="排序">
              <a-input-number v-model:value="selectedItem.sort" :min="0" @change="updateSelectedItemSort" />
            </a-form-item>
          </a-form>

          <a-button type="primary" danger block @click="removeSelectedItem">
            <i class="fa fa-trash mr-1"></i>删除元素
          </a-button>
        </a-card>

        <!-- 布局信息 -->
        <a-card title="布局信息" size="small">
          <div class="text-sm text-gray-600">
            <p>总元素数: {{ gridItems.length }}</p>
            <p>栅格列数: 24</p>
          </div>
        </a-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { message } from 'ant-design-vue'
import { GridStack } from 'gridstack'
import 'gridstack/dist/gridstack.min.css'
import { GridStackDebugger, validateImageData } from '~@/utils/gridstack-debug.js'

// Props
const props = defineProps({
  uploadFunction: {
    type: Function,
    default: null
  },
  initialImages: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['save', 'change'])

// 响应式数据
const gridStackContainer = ref(null)
const gridStack = ref(null)
const imageLibrary = ref([])
const gridItems = ref([])
const selectedItem = ref(null)

// 栅格配置
const gridConfig = ref({
  column: 24,
  cellHeight: 60,
  margin: 5,
  animate: true,
  float: false,
  removable: false,
  resizable: false,
  draggable: false
})

// 存储待加载的初始图片
const pendingImages = ref([])

// 监听初始图片变化
watch(() => props.initialImages, (newImages) => {
  if (newImages && newImages.length > 0) {
    pendingImages.value = newImages
    if (gridStack.value) {
      loadInitialImages(newImages)
    }
  }
}, { immediate: true })

// 加载初始图片
const loadInitialImages = (images) => {
  console.log('加载初始图片:', images)
  imageLibrary.value = []
  gridItems.value = []

  if (gridStack.value) {
    gridStack.value.removeAll()
  }

  images.forEach((item, index) => {
    const imageData = {
      name: `image-${index}`,
      url: item.img,
      sort: item.sort || 0
    }

    // 添加到图片库
    imageLibrary.value.push(imageData)

    // 如果有布局信息且GridStack已初始化，直接添加到栅格
    if (item.layout && gridStack.value) {
      const widget = {
        x: Math.max(0, item.layout.x || 0),
        y: Math.max(0, item.layout.y || 0),
        w: Math.max(1, Math.min(24, item.layout.w || 6)),
        h: Math.max(1, Math.min(20, item.layout.h || 4)),
        content: createImageContent(imageData)
        // 移除额外属性，只保留GridStack需要的基本属性
      }

      console.log('添加初始widget:', widget)
      try {
        const addedElement = gridStack.value.addWidget(widget)
        if (addedElement) {
          // 存储图片信息到gridItems
          const gridItem = {
            id: addedElement.getAttribute('gs-id'),
            image: imageData,
            element: addedElement,
            x: widget.x,
            y: widget.y,
            w: widget.w,
            h: widget.h
          }
          gridItems.value.push(gridItem)
        }
      } catch (error) {
        console.error('添加初始widget失败:', error)
      }
    }
  })
}

// 计算图片实际大小
const calculateImageSize = (image) => {
  return new Promise((resolve) => {
    // 检查图片数据是否有效
    if (!image || !image.url) {
      console.error('图片数据无效，使用默认尺寸:', image)
      resolve({ w: 6, h: 4 })
      return
    }

    const img = new Image()
    img.onload = () => {
      const aspectRatio = img.width / img.height
      let gridWidth = 6  // 默认宽度
      let gridHeight = 4 // 默认高度

      // 根据宽高比调整
      if (aspectRatio > 1) {
        // 横向图片，优先保证宽度
        gridHeight = Math.max(Math.ceil(gridWidth / aspectRatio), 2)
      } else {
        // 纵向图片，优先保证高度
        gridWidth = Math.max(Math.ceil(gridHeight * aspectRatio), 2)
      }

      resolve({ w: gridWidth, h: gridHeight })
    }
    img.onerror = () => {
      // 如果图片加载失败，使用默认尺寸
      console.error('图片加载失败，使用默认尺寸:', image.url)
      resolve({ w: 6, h: 4 })
    }
    img.src = image.url
  })
}

const handleImageUpload = async (file) => {
  try {
    console.log('开始上传图片:', file)

    // 调用父组件传入的上传函数
    if (props.uploadFunction) {
      const result = await props.uploadFunction(file)
      console.log('上传结果:', result)

      // 确保result有有效的URL
      let imageUrl = ''
      if (typeof result === 'string') {
        imageUrl = result
      } else if (result && result.url) {
        imageUrl = result.url
      } else {
        throw new Error('上传结果格式无效')
      }

      if (!imageUrl) {
        throw new Error('上传结果中没有有效的URL')
      }

      const imageData = {
        name: file.name,
        url: imageUrl,
        sort: 0
      }

      console.log('创建的图片数据:', imageData)
      imageLibrary.value.push(imageData)
      message.success('图片上传成功')
    } else {
      message.error('未配置上传函数')
    }

    return false // 阻止默认上传
  } catch (error) {
    console.error('图片上传失败:', error)
    message.error('图片上传失败: ' + (error.message || '未知错误'))
    return false
  }
}

const handleDragStart = (event, image) => {
  event.dataTransfer.setData('application/json', JSON.stringify(image))
}

const addImageToGrid = async (image) => {
  console.log('开始添加图片到栅格:', image)

  // 使用调试工具验证图片数据
  if (!validateImageData(image)) {
    message.error('图片数据验证失败')
    return
  }

  if (!gridStack.value) {
    console.error('GridStack未初始化')
    message.error('栅格系统未初始化')

    // 创建调试器进行诊断
    const gridDebugger = new GridStackDebugger(gridStack.value, 'DetailImageGridEditor')
    gridDebugger.checkGridStackStatus()
    gridDebugger.generateReport()
    return
  }

  try {
    // 计算图片的实际大小
    console.log('计算图片尺寸...')
    const size = await calculateImageSize(image)
    console.log('图片尺寸计算结果:', size)

    // 创建一个唯一的ID
    const widgetId = `widget-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    // 创建图片内容
    console.log('准备创建图片内容，图片数据:', image)
    console.log('图片URL类型:', typeof image.url, '值:', image.url)

    const content = createImageContent(image)
    console.log('生成的内容长度:', content ? content.length : 0)
    console.log('生成的内容预览:', content ? content.substring(0, 100) : '无内容')

    // 确保所有值都是有效的 - 使用最简单的widget配置
    const widget = {
      x: 0,
      y: 0,
      w: Math.max(1, Math.min(24, size.w || 6)), // 确保宽度在有效范围内
      h: Math.max(1, Math.min(20, size.h || 4)), // 确保高度在有效范围内
      content: content || '<div class="grid-item-content">空内容</div>'
      // 移除id属性，让GridStack自动生成
    }

    // 验证widget对象的所有属性都是有效数字
    if (!Number.isInteger(widget.x) || !Number.isInteger(widget.y) ||
      !Number.isInteger(widget.w) || !Number.isInteger(widget.h)) {
      throw new Error('Widget位置或尺寸包含无效数值')
    }

    // 验证content是字符串
    if (typeof widget.content !== 'string' || widget.content.length === 0) {
      throw new Error('Widget内容无效')
    }

    console.log('验证通过的widget:', widget)

    // 添加widget - 使用最简单的方式
    console.log('准备调用addWidget，GridStack实例:', !!gridStack.value)

    let addedElement = null
    try {
      // 直接传递widget对象，不包含可能导致问题的额外属性
      addedElement = gridStack.value.addWidget(widget)
      console.log('addWidget调用成功，返回结果:', addedElement)

      if (!addedElement) {
        throw new Error('addWidget返回了空值')
      }
    } catch (addWidgetError) {
      console.error('addWidget调用失败:', addWidgetError)
      console.error('addWidget错误堆栈:', addWidgetError.stack)

      // 提供更详细的错误信息
      if (addWidgetError.message.includes('substring')) {
        console.error('这是一个GridStack拖拽相关的错误，可能是由于配置冲突导致的')
        console.error('当前GridStack配置:', gridConfig.value)
      }

      throw addWidgetError
    }

    // 存储图片信息到gridItems数组中
    const gridItem = {
      id: addedElement.getAttribute('gs-id') || widgetId, // 使用GridStack生成的ID
      image: image,
      element: addedElement,
      x: widget.x,
      y: widget.y,
      w: widget.w,
      h: widget.h
    }

    gridItems.value.push(gridItem)
    console.log('图片信息已存储到gridItems:', gridItem)
    console.log('当前gridItems总数:', gridItems.value.length)

    message.success('图片已添加到布局')

  } catch (error) {
    console.error('添加图片到栅格失败:', error)
    console.error('错误堆栈:', error.stack)

    // 提供用户友好的错误信息
    let errorMessage = '添加图片失败'
    if (error.message.includes('substring')) {
      errorMessage = '添加图片失败：GridStack配置错误，请刷新页面重试'
    } else if (error.message.includes('Widget')) {
      errorMessage = '添加图片失败：图片数据无效'
    } else {
      errorMessage = '添加图片失败: ' + (error.message || '未知错误')
    }

    message.error(errorMessage)
  }
}

const createImageContent = (image) => {
  console.log('createImageContent被调用，参数:', image)

  // 安全地处理图片URL
  if (!image || !image.url) {
    console.error('图片数据无效:', image)
    return '<div class="grid-item-content h-full w-full overflow-hidden bg-gray-200 flex items-center justify-center">无效图片</div>'
  }

  console.log('图片URL验证通过:', image.url)
  console.log('图片URL类型:', typeof image.url)

  // 处理图片URL，如果已经包含缩略图参数则使用原URL，否则添加缩略图参数
  let imageUrl = ''
  try {
    imageUrl = image.url && image.url.includes('!') ? image.url : `${image.url}!s200`
    console.log('处理后的图片URL:', imageUrl)
  } catch (urlError) {
    console.error('处理图片URL时出错:', urlError)
    imageUrl = image.url || ''
  }

  return `
    <div class="grid-item-content h-full w-full overflow-hidden">
      <img src="${imageUrl}" alt="${image.name || '图片'}" class="w-full h-full object-cover" />
      <div class="grid-item-overlay absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all flex items-center justify-center">
        <div class="opacity-0 hover:opacity-100 transition-opacity">
          <span class="text-white bg-blue-500 px-2 py-1 rounded text-xs">排序: ${image.sort || 0}</span>
        </div>
      </div>
    </div>
  `
}

const removeFromLibrary = (index) => {
  imageLibrary.value.splice(index, 1)
  message.success('图片已移除')
}

const updateGridConfig = () => {
  if (gridStack.value) {
    gridStack.value.cellHeight(gridConfig.value.cellHeight)
    gridStack.value.margin(gridConfig.value.margin)
  }
}

const updateSelectedItem = () => {
  if (selectedItem.value && gridStack.value) {
    gridStack.value.update(selectedItem.value.el, {
      w: selectedItem.value.w,
      h: selectedItem.value.h
    })
  }
}

const updateSelectedItemSort = () => {
  if (selectedItem.value) {
    selectedItem.value.image.sort = selectedItem.value.sort
    // 更新显示内容
    const content = createImageContent(selectedItem.value.image)
    selectedItem.value.el.querySelector('.grid-item-content').outerHTML = content
  }
}

const removeSelectedItem = () => {
  if (selectedItem.value && gridStack.value) {
    gridStack.value.removeWidget(selectedItem.value.el)
    const index = gridItems.value.findIndex(item => item === selectedItem.value)
    if (index > -1) {
      gridItems.value.splice(index, 1)
    }
    selectedItem.value = null
    message.success('元素已删除')
  }
}

const clearGrid = () => {
  if (gridStack.value) {
    gridStack.value.removeAll()
    gridItems.value = []
    selectedItem.value = null
    message.success('栅格已清空')
  }
}

const testAddWidget = () => {
  console.log('开始测试添加widget')

  if (!gridStack.value) {
    console.error('GridStack未初始化')
    message.error('GridStack未初始化')
    return
  }

  // 创建一个最简单的widget测试
  try {
    const simpleWidget = {
      x: 0,
      y: 0,
      w: 4,
      h: 3,
      content: '<div style="background: #f0f0f0; height: 100%; display: flex; align-items: center; justify-content: center; border-radius: 4px;">测试Widget</div>'
      // 移除id属性，让GridStack自动生成
    }

    console.log('测试widget:', simpleWidget)
    const result = gridStack.value.addWidget(simpleWidget)
    console.log('测试widget添加结果:', result)

    if (result) {
      message.success('测试widget添加成功')
    } else {
      message.error('测试widget添加失败')
    }
  } catch (error) {
    console.error('测试widget添加失败:', error)
    console.error('测试widget错误堆栈:', error.stack)

    let errorMessage = '测试失败'
    if (error.message.includes('substring')) {
      errorMessage = '测试失败：GridStack拖拽配置错误'
    } else {
      errorMessage = '测试失败: ' + error.message
    }

    message.error(errorMessage)
  }
}

const saveLayout = () => {
  const layout = {
    config: gridConfig.value,
    items: gridStack.value.save(),
    images: imageLibrary.value
  }

  emit('save', layout)
  message.success('布局已保存')
}

// 获取当前布局数据
const getLayoutData = () => {
  if (!gridStack.value || !gridItems.value.length) return []

  try {
    // 直接从gridItems获取数据，这样更可靠
    const layoutData = gridItems.value.map(item => {
      // 获取当前元素的位置信息
      const element = item.element
      let x = item.x, y = item.y, w = item.w, h = item.h

      // 如果元素存在，尝试从DOM获取最新位置
      if (element && element.getAttribute) {
        x = parseInt(element.getAttribute('gs-x')) || x
        y = parseInt(element.getAttribute('gs-y')) || y
        w = parseInt(element.getAttribute('gs-w')) || w
        h = parseInt(element.getAttribute('gs-h')) || h
      }

      return {
        img: item.image?.url || '',
        sort: item.image?.sort || 0,
        layout: { x, y, w, h }
      }
    }).filter(item => item.img) // 过滤掉没有图片的项

    console.log('获取的布局数据:', layoutData)
    return layoutData

  } catch (error) {
    console.error('获取布局数据失败:', error)
    return []
  }
}

// 暴露方法给父组件
defineExpose({
  getLayoutData,
  saveLayout
})

// 生命周期
onMounted(async () => {
  await nextTick()

  if (gridStackContainer.value) {
    try {
      // 使用最基础的GridStack配置，确保与gridConfig一致
      const config = {
        column: gridConfig.value.column,
        cellHeight: gridConfig.value.cellHeight,
        margin: gridConfig.value.margin,
        animate: gridConfig.value.animate,
        float: gridConfig.value.float,
        removable: gridConfig.value.removable,
        resizable: gridConfig.value.resizable,
        draggable: gridConfig.value.draggable,
        // 禁用拖拽相关功能以避免_DDDraggable错误
        acceptWidgets: false,
        dragIn: false,
        dragInOptions: false
      }

      console.log('GridStack配置:', config)
      console.log('GridStack容器:', gridStackContainer.value)

      // 检查GridStack版本
      console.log('GridStack版本:', GridStack.version || 'unknown')

      gridStack.value = GridStack.init(config, gridStackContainer.value)
      console.log('GridStack初始化完成:', !!gridStack.value)

      // 监听事件
      gridStack.value.on('added', (event, items) => {
        console.log('GridStack added事件:', event, items)
      })

      gridStack.value.on('change', (event, items) => {
        console.log('GridStack change事件:', event, items)
        emit('change', {
          config: gridConfig.value,
          items: getLayoutData(),
          images: imageLibrary.value
        })
      })

      // 使用事件委托处理点击事件
      gridStackContainer.value.addEventListener('click', (e) => {
        const gridItem = e.target.closest('.grid-stack-item')
        if (gridItem) {
          // 清除其他选中状态
          document.querySelectorAll('.grid-stack-item').forEach(el => {
            el.classList.remove('selected')
          })

          // 设置当前选中
          gridItem.classList.add('selected')

          // 查找对应的gridItem数据
          const itemData = gridItems.value.find(item =>
            item.element === gridItem || item.id === gridItem.getAttribute('gs-id')
          )

          if (itemData) {
            selectedItem.value = {
              el: gridItem,
              image: itemData.image,
              sort: itemData.image?.sort || 0,
              w: itemData.w,
              h: itemData.h
            }
          }
        }
      })

      gridStack.value.on('removed', (event, items) => {
        console.log('GridStack removed事件:', event, items)
        if (selectedItem.value) {
          selectedItem.value = null
        }

        // 从gridItems中移除对应的项
        const itemsArray = Array.isArray(items) ? items : [items]
        itemsArray.forEach(item => {
          const index = gridItems.value.findIndex(gi => gi.element === item.el)
          if (index > -1) {
            gridItems.value.splice(index, 1)
          }
        })
      })

      // GridStack初始化完成后，加载待处理的图片
      if (pendingImages.value.length > 0) {
        loadInitialImages(pendingImages.value)
        pendingImages.value = []
      }
    } catch (error) {
      console.error('GridStack初始化失败:', error)
      message.error('栅格系统初始化失败')
    }
  }
})

onUnmounted(() => {
  if (gridStack.value) {
    gridStack.value.destroy()
  }
})
</script>

<style scoped>
.detail-image-grid-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.toolbar {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.editor-content {
  flex: 1;
  overflow: hidden;
}

.image-library {
  background: #fafafa;
  border-right: 1px solid #e8e8e8;
}

.image-library h4 {
  color: #262626;
  font-weight: 600;
}

.image-item {
  transition: all 0.3s ease;
  background: white;
  border: 1px solid #e8e8e8;
}

.image-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #1890ff;
}

.grid-container {
  background: white;
}

.grid-wrapper {
  background: #f8f9fa;
  border: 1px solid #e8e8e8;
}

.grid-stack {
  background: linear-gradient(90deg, #f0f0f0 1px, transparent 1px),
    linear-gradient(#f0f0f0 1px, transparent 1px);
  background-size: 20px 20px;
}

.grid-stack-item.selected {
  border: 2px solid #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  z-index: 999 !important;
}

.grid-item-content {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.grid-item-content:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.grid-item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0);
  transition: all 0.3s ease;
}

.grid-item-content:hover .grid-item-overlay {
  background: rgba(0, 0, 0, 0.1);
}

.config-panel {
  background: #fafafa;
  border-left: 1px solid #e8e8e8;
}

.config-panel h4 {
  color: #262626;
  font-weight: 600;
}

/* GridStack 样式覆盖 */
:deep(.grid-stack-item-content) {
  background: transparent;
  border: none;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: none;
}

:deep(.grid-stack-item) {
  border-radius: 6px;
}

:deep(.grid-stack-item-removing) {
  opacity: 0.5;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

:deep(.grid-stack-item.ui-draggable-dragging) {
  transform: rotate(5deg);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

:deep(.grid-stack-item.ui-resizable-resizing) {
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

/* 拖拽辅助线 */
:deep(.grid-stack-placeholder) {
  background: rgba(24, 144, 255, 0.1) !important;
  border: 2px dashed #1890ff !important;
  border-radius: 6px;
}

/* 调整手柄样式 */
:deep(.ui-resizable-handle) {
  background: #1890ff;
  border-radius: 50%;
  width: 8px !important;
  height: 8px !important;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

:deep(.ui-resizable-se) {
  right: -4px !important;
  bottom: -4px !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .image-library {
    width: 240px;
  }

  .config-panel {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .editor-content {
    flex-direction: column;
  }

  .image-library,
  .config-panel {
    width: 100%;
    height: 200px;
  }

  .grid-container {
    flex: 1;
    min-height: 400px;
  }
}

/* 动画效果 */
.image-item {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>