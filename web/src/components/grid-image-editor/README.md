# 栅格化图片布局编辑器

基于Vue3 + Ant Design + Quill + GridStack.js开发的栅格化图片布局编辑器组件。

## 功能特性

### 🎯 核心功能
- **栅格布局系统**: 基于GridStack.js的拖拽式栅格布局（固定24列）
- **图片管理**: 支持图片上传、预览、删除
- **智能尺寸**: 添加图片时自动计算并展示图片实际大小
- **实时编辑**: 拖拽调整图片位置和大小
- **配置面板**: 可视化配置栅格参数和元素属性
- **数据导出**: 支持布局数据的保存和导出

### 🛠️ 技术栈
- **Vue 3**: 组合式API
- **Ant Design Vue**: UI组件库
- **GridStack.js**: 栅格布局引擎
- **Quill**: 富文本编辑器（集成在父页面）

## 组件API

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| uploadFunction | Function | null | 图片上传函数 |
| initialImages | Array | [] | 初始图片列表 |
| initialLayout | Object | {} | 初始布局配置 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| save | layout | 保存布局时触发 |
| export | layout | 导出布局时触发 |
| change | layout | 布局变化时触发 |

### 布局数据结构

```javascript
{
  config: {
    column: 24,        // 栅格列数（固定24列）
    cellHeight: 60,    // 单元格高度
    margin: 5,         // 间距
    animate: true,     // 动画效果
    float: false,      // 浮动模式
    removable: true,   // 可删除
    resizable: true,   // 可调整大小
    draggable: true    // 可拖拽
  },
  items: [             // 栅格项目
    {
      x: 0, y: 0,      // 位置
      w: 3, h: 3,      // 尺寸
      content: "...",  // HTML内容
      image: {...}     // 图片信息
    }
  ],
  images: [            // 图片库
    {
      name: "image.jpg",
      url: "https://...",
      size: 1024,
      type: "image/jpeg"
    }
  ]
}
```

## 使用示例

### 基础用法

```vue
<template>
  <GridImageEditor
    :upload-function="handleImageUpload"
    @save="handleSave"
    @export="handleExport"
    @change="handleChange"
  />
</template>

<script setup>
import { GridImageEditor } from '@/components/grid-image-editor'

const handleImageUpload = async (file) => {
  // 实现图片上传逻辑
  const formData = new FormData()
  formData.append('image', file)

  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData
  })

  const result = await response.json()
  return result.url
}

const handleSave = (layout) => {
  console.log('保存布局:', layout)
  // 保存到数据库
}

const handleExport = (layout) => {
  console.log('导出布局:', layout)
  // 导出处理
}

const handleChange = (layout) => {
  console.log('布局变化:', layout)
  // 实时保存或其他处理
}
</script>
```

### 与Quill编辑器集成

```vue
<template>
  <a-modal :open="visible" title="内容编辑器" width="90%">
    <a-tabs v-model:activeKey="activeTab">
      <a-tab-pane key="richtext" tab="富文本编辑器">
        <QuillEditor v-model:content="content" />
      </a-tab-pane>

      <a-tab-pane key="gridlayout" tab="栅格化图片布局">
        <GridImageEditor
          :upload-function="uploadImageToUpyun"
          @save="handleGridLayoutSave"
        />
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>
```

## 操作指南

### 1. 添加图片
- 点击"添加图片"按钮选择本地图片
- 图片会自动上传并添加到图片库
- 支持多选和拖拽上传

### 2. 创建栅格项
- 从图片库拖拽图片到栅格区域
- 或点击图片的"添加"按钮
- 图片会自动计算实际尺寸并创建为栅格项

### 3. 编辑栅格项
- 拖拽调整位置
- 拖拽边角调整大小
- 点击选中后在右侧配置面板调整属性

### 4. 栅格配置
- 调整行高、间距等参数（列数固定为24列）
- 实时预览配置效果

### 5. 保存和导出
- 保存：将布局数据传递给父组件
- 导出：下载JSON格式的布局文件

## 🎨 特色功能

1. **可视化栅格**: 带网格背景的24列栅格系统
2. **智能尺寸**: 自动计算图片实际尺寸，完美适配栅格
3. **拖拽体验**: 流畅的拖拽和调整大小动画
4. **响应式设计**: 支持不同屏幕尺寸
5. **丰富配置**: 栅格参数和元素属性可视化配置

## 样式定制

组件提供了丰富的CSS类名用于样式定制：

```css
/* 栅格容器 */
.grid-stack {
  background: #f8f9fa;
}

/* 栅格项 */
.grid-stack-item {
  border-radius: 4px;
}

/* 选中状态 */
.grid-stack-item.selected {
  border: 2px solid #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 图片项 */
.image-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
```

## 注意事项

1. **依赖安装**: 确保已安装 `gridstack` 依赖
2. **样式引入**: 组件会自动引入GridStack的CSS样式
3. **图片上传**: 需要提供 `uploadFunction` 实现图片上传
4. **浏览器兼容**: 支持现代浏览器，IE需要polyfill
5. **性能优化**: 大量图片时建议实现虚拟滚动

## 更新日志

### v1.1.0
- 🔥 删除预览模式，简化操作流程
- 🔥 固定栅格为24列，删除列数配置
- ✨ 新增智能图片尺寸计算功能
- ✨ 添加图片时自动展示实际大小
- 🎨 优化用户界面和交互体验

### v1.0.0
- 初始版本发布
- 基础栅格布局功能
- 图片管理功能
- 配置面板
- 保存和导出功能
