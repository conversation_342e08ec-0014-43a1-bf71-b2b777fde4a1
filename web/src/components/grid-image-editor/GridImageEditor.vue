<template>
  <div class="grid-image-editor">
    <!-- 工具栏 -->
    <div class="toolbar bg-white border-b border-gray-200 p-4 flex justify-between items-center">
      <div class="flex items-center space-x-4">
        <h3 class="text-lg font-medium text-gray-800">栅格化图片布局编辑器</h3>
      </div>

      <div class="flex items-center space-x-2">
        <a-button @click="clearGrid">
          <i class="fa fa-trash mr-1"></i>清空
        </a-button>
        <a-button type="primary" @click="saveLayout">
          <i class="fa fa-save mr-1"></i>保存布局
        </a-button>
        <a-button @click="exportLayout">
          <i class="fa fa-download mr-1"></i>导出
        </a-button>
      </div>
    </div>

    <div class="editor-content flex h-[calc(100vh-200px)]">
      <!-- 左侧图片库 -->
      <div class="image-library w-80 bg-gray-50 border-r border-gray-200 p-4 overflow-y-auto">
        <div class="mb-4">
          <h4 class="text-md font-medium text-gray-700 mb-3">图片库</h4>

          <!-- 图片上传区域 -->
          <a-upload :before-upload="handleImageUpload" :show-upload-list="false" accept="image/*" multiple class="mb-4">
            <a-button type="dashed" block>
              <i class="fa fa-plus mr-2"></i>添加图片
            </a-button>
          </a-upload>
        </div>

        <!-- 图片列表 -->
        <div class="image-list grid grid-cols-2 gap-3">
          <div v-for="(image, index) in imageLibrary" :key="index"
            class="image-item relative group cursor-pointer border border-gray-200 rounded-lg overflow-hidden hover:border-blue-400 transition-colors"
            draggable="true" @dragstart="handleDragStart($event, image)">
            <img :src="image.url" :alt="image.name" class="w-full h-20 object-cover" />
            <div
              class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity">
                <a-button type="primary" size="small" @click="addImageToGrid(image)">
                  添加
                </a-button>
              </div>
            </div>
            <div class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <a-button type="primary" danger size="small" @click="removeFromLibrary(index)">
                <i class="fa fa-times"></i>
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间栅格编辑区域 -->
      <div class="grid-container flex-1 p-4 bg-white">
        <div class="grid-wrapper h-full border border-gray-300 rounded-lg overflow-hidden">
          <div ref="gridStackContainer" class="grid-stack h-full"></div>
        </div>
      </div>

      <!-- 右侧配置面板 -->
      <div class="config-panel w-80 bg-gray-50 border-l border-gray-200 p-4 overflow-y-auto">
        <h4 class="text-md font-medium text-gray-700 mb-4">配置面板</h4>

        <!-- 栅格配置 -->
        <a-card title="栅格设置" size="small" class="mb-4">
          <a-form layout="vertical" size="small">
            <a-form-item label="行高">
              <a-input-number v-model:value="gridConfig.cellHeight" :min="20" :max="200" @change="updateGridConfig" />
            </a-form-item>
            <a-form-item label="间距">
              <a-input-number v-model:value="gridConfig.margin" :min="0" :max="20" @change="updateGridConfig" />
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 选中元素配置 -->
        <a-card v-if="selectedItem" title="元素设置" size="small" class="mb-4">
          <a-form layout="vertical" size="small">
            <a-form-item label="宽度">
              <a-input-number v-model:value="selectedItem.w" :min="1" :max="24" @change="updateSelectedItem" />
            </a-form-item>
            <a-form-item label="高度">
              <a-input-number v-model:value="selectedItem.h" :min="1" :max="20" @change="updateSelectedItem" />
            </a-form-item>
            <a-form-item label="圆角">
              <a-slider v-model:value="selectedItem.borderRadius" :min="0" :max="50"
                @change="updateSelectedItemStyle" />
            </a-form-item>
            <a-form-item label="透明度">
              <a-slider v-model:value="selectedItem.opacity" :min="0" :max="1" :step="0.1"
                @change="updateSelectedItemStyle" />
            </a-form-item>
          </a-form>

          <a-button type="primary" danger block @click="removeSelectedItem">
            <i class="fa fa-trash mr-1"></i>删除元素
          </a-button>
        </a-card>

        <!-- 布局信息 -->
        <a-card title="布局信息" size="small">
          <div class="text-sm text-gray-600">
            <p>总元素数: {{ gridItems.length }}</p>
            <p>栅格列数: 24</p>
          </div>
        </a-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { GridStack } from 'gridstack'
import 'gridstack/dist/gridstack.min.css'

// Props
const props = defineProps({
  uploadFunction: {
    type: Function,
    default: null
  },
  initialImages: {
    type: Array,
    default: () => []
  },
  initialLayout: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['save', 'export', 'change'])

// 响应式数据
const gridStackContainer = ref(null)
const gridStack = ref(null)
const imageLibrary = ref([])
const gridItems = ref([])
const selectedItem = ref(null)

// 栅格配置
const gridConfig = ref({
  column: 24,
  cellHeight: 60,
  margin: 5,
  animate: true,
  float: false,
  removable: true,
  resizable: true,
  draggable: true
})

// 计算图片在栅格中的实际大小
const calculateImageSize = (image) => {
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => {
      // 计算图片的宽高比
      const aspectRatio = img.width / img.height

      // 基于栅格系统计算合适的尺寸
      // 假设每个栅格单元约为40px宽度（可根据实际情况调整）
      const gridUnitWidth = 40
      const gridUnitHeight = gridConfig.value.cellHeight

      // 计算图片应该占用的栅格数
      let gridWidth = Math.ceil(img.width / gridUnitWidth)
      let gridHeight = Math.ceil(img.height / gridUnitHeight)

      // 限制最大尺寸
      gridWidth = Math.min(gridWidth, 12) // 最大12列
      gridHeight = Math.min(gridHeight, 10) // 最大10行

      // 确保最小尺寸
      gridWidth = Math.max(gridWidth, 2) // 最小2列
      gridHeight = Math.max(gridHeight, 2) // 最小2行

      // 根据宽高比调整
      if (aspectRatio > 1) {
        // 横向图片，优先保证宽度
        gridHeight = Math.max(Math.ceil(gridWidth / aspectRatio), 2)
      } else {
        // 纵向图片，优先保证高度
        gridWidth = Math.max(Math.ceil(gridHeight * aspectRatio), 2)
      }

      resolve({ w: gridWidth, h: gridHeight })
    }
    img.onerror = () => {
      // 如果图片加载失败，使用默认尺寸
      resolve({ w: 4, h: 3 })
    }
    img.src = image.url
  })
}

const handleImageUpload = async (file) => {
  try {
    // 调用父组件传入的上传函数，或使用默认的本地URL
    let imageUrl
    if (props.uploadFunction) {
      imageUrl = await props.uploadFunction(file)
    } else {
      imageUrl = URL.createObjectURL(file)
    }

    imageLibrary.value.push({
      name: file.name,
      url: imageUrl,
      size: file.size,
      type: file.type
    })

    message.success('图片添加成功')
    return false // 阻止默认上传
  } catch (error) {
    message.error('图片添加失败')
    return false
  }
}

const handleDragStart = (event, image) => {
  event.dataTransfer.setData('application/json', JSON.stringify(image))
}

const addImageToGrid = async (image) => {
  if (!gridStack.value) return

  // 计算图片的实际大小
  const size = await calculateImageSize(image)

  const widget = {
    x: 0,
    y: 0,
    w: size.w,
    h: size.h,
    content: createImageContent(image),
    image: image,
    borderRadius: 0,
    opacity: 1
  }

  gridStack.value.addWidget(widget)
  gridItems.value.push(widget)
}

const createImageContent = (image) => {
  return `
    <div class="grid-item-content h-full w-full overflow-hidden">
      <img src="${image.url}" alt="${image.name}" class="w-full h-full object-cover" />
      <div class="grid-item-overlay absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all flex items-center justify-center">
        <div class="opacity-0 hover:opacity-100 transition-opacity">
          <button class="text-white bg-blue-500 px-2 py-1 rounded text-xs">编辑</button>
        </div>
      </div>
    </div>
  `
}

const removeFromLibrary = (index) => {
  imageLibrary.value.splice(index, 1)
  message.success('图片已移除')
}

const updateGridConfig = () => {
  if (gridStack.value) {
    gridStack.value.cellHeight(gridConfig.value.cellHeight)
    gridStack.value.margin(gridConfig.value.margin)
  }
}

const updateSelectedItem = () => {
  if (selectedItem.value && gridStack.value) {
    gridStack.value.update(selectedItem.value.el, {
      w: selectedItem.value.w,
      h: selectedItem.value.h
    })
  }
}

const updateSelectedItemStyle = () => {
  if (selectedItem.value) {
    const img = selectedItem.value.el.querySelector('img')
    if (img) {
      img.style.borderRadius = `${selectedItem.value.borderRadius}px`
      img.style.opacity = selectedItem.value.opacity
    }
  }
}

const removeSelectedItem = () => {
  if (selectedItem.value && gridStack.value) {
    gridStack.value.removeWidget(selectedItem.value.el)
    const index = gridItems.value.findIndex(item => item === selectedItem.value)
    if (index > -1) {
      gridItems.value.splice(index, 1)
    }
    selectedItem.value = null
    message.success('元素已删除')
  }
}

const clearGrid = () => {
  if (gridStack.value) {
    gridStack.value.removeAll()
    gridItems.value = []
    selectedItem.value = null
    message.success('栅格已清空')
  }
}

const saveLayout = () => {
  const layout = {
    config: gridConfig.value,
    items: gridStack.value.save(),
    images: imageLibrary.value
  }

  emit('save', layout)
  message.success('布局已保存')
}

const exportLayout = () => {
  const layout = {
    config: gridConfig.value,
    items: gridStack.value.save(),
    images: imageLibrary.value
  }

  emit('export', layout)

  // 同时提供本地下载功能
  const dataStr = JSON.stringify(layout, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)

  const link = document.createElement('a')
  link.href = url
  link.download = 'grid-layout.json'
  link.click()

  URL.revokeObjectURL(url)
  message.success('布局已导出')
}

// 生命周期
onMounted(async () => {
  await nextTick()

  if (gridStackContainer.value) {
    try {
      gridStack.value = GridStack.init({
        ...gridConfig.value,
        acceptWidgets: true,
        dragIn: '.image-item',
        dragInOptions: {
          revert: 'invalid',
          scroll: false,
          appendTo: 'body',
          helper: 'clone'
        }
      }, gridStackContainer.value)

      // 监听事件
      gridStack.value.on('added', (event, items) => {
        items.forEach(item => {
          item.el.addEventListener('click', () => {
            // 清除其他选中状态
            document.querySelectorAll('.grid-stack-item').forEach(el => {
              el.classList.remove('selected')
            })

            // 设置当前选中
            item.el.classList.add('selected')
            selectedItem.value = {
              ...item,
              borderRadius: 0,
              opacity: 1
            }
          })
        })
      })

      gridStack.value.on('removed', (event, items) => {
        if (selectedItem.value && items.some(item => item.el === selectedItem.value.el)) {
          selectedItem.value = null
        }
      })

      gridStack.value.on('change', (event, items) => {
        emit('change', {
          config: gridConfig.value,
          items: gridStack.value.save(),
          images: imageLibrary.value
        })
      })
    } catch (error) {
      console.error('GridStack初始化失败:', error)
      message.error('栅格系统初始化失败')
    }
  }
})

onUnmounted(() => {
  if (gridStack.value) {
    gridStack.value.destroy()
  }
})
</script>

<style scoped>
.grid-image-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.toolbar {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.editor-content {
  flex: 1;
  overflow: hidden;
}

.image-library {
  background: #fafafa;
  border-right: 1px solid #e8e8e8;
}

.image-library h4 {
  color: #262626;
  font-weight: 600;
}

.image-item {
  transition: all 0.3s ease;
  background: white;
  border: 1px solid #e8e8e8;
}

.image-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #1890ff;
}

.grid-container {
  background: white;
}

.grid-wrapper {
  background: #f8f9fa;
  border: 1px solid #e8e8e8;
}

.grid-stack {
  background: linear-gradient(90deg, #f0f0f0 1px, transparent 1px),
    linear-gradient(#f0f0f0 1px, transparent 1px);
  background-size: 20px 20px;
}



.grid-stack-item.selected {
  border: 2px solid #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  z-index: 999 !important;
}

.grid-item-content {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.grid-item-content:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.grid-item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0);
  transition: all 0.3s ease;
}

.grid-item-content:hover .grid-item-overlay {
  background: rgba(0, 0, 0, 0.1);
}

.config-panel {
  background: #fafafa;
  border-left: 1px solid #e8e8e8;
}

.config-panel h4 {
  color: #262626;
  font-weight: 600;
}

/* GridStack 样式覆盖 */
:deep(.grid-stack-item-content) {
  background: transparent;
  border: none;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: none;
}

:deep(.grid-stack-item) {
  border-radius: 6px;
}

:deep(.grid-stack-item-removing) {
  opacity: 0.5;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

:deep(.grid-stack-item.ui-draggable-dragging) {
  transform: rotate(5deg);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

:deep(.grid-stack-item.ui-resizable-resizing) {
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

/* 拖拽辅助线 */
:deep(.grid-stack-placeholder) {
  background: rgba(24, 144, 255, 0.1) !important;
  border: 2px dashed #1890ff !important;
  border-radius: 6px;
}

/* 调整手柄样式 */
:deep(.ui-resizable-handle) {
  background: #1890ff;
  border-radius: 50%;
  width: 8px !important;
  height: 8px !important;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

:deep(.ui-resizable-se) {
  right: -4px !important;
  bottom: -4px !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .image-library {
    width: 240px;
  }

  .config-panel {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .editor-content {
    flex-direction: column;
  }

  .image-library,
  .config-panel {
    width: 100%;
    height: 200px;
  }

  .grid-container {
    flex: 1;
    min-height: 400px;
  }
}

/* 动画效果 */
.image-item {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
</style>
