.ant-pro-basicLayout{
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100%;
  .ant-layout{
    background: #f0f2f5;
  }

  &-content{
    // position: relative;
    margin: 24px;
    display: flex;

    &-fluid{
      width: 100%;
    }

    &-fixed{
      width: 1200px;
      max-width: 1200px;
      margin: 0 auto;

      &:has(.ant-pro-page-container){
        width: 100%;
        max-width: unset;
        margin: unset;
      }
    }
  }
}

[data-theme='dark']{
  .ant-layout{
    background: rgb(42, 44, 44);
  }
}

