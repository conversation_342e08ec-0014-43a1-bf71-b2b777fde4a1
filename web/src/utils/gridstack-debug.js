/**
 * GridStack调试工具
 * 用于诊断GridStack相关问题
 */

export class GridStackDebugger {
  constructor(gridStackInstance, componentName = 'GridStack') {
    this.gridStack = gridStackInstance
    this.componentName = componentName
    this.logs = []
  }

  // 记录日志
  log (message, data = null) {
    const timestamp = new Date().toISOString()
    const logEntry = {
      timestamp,
      message,
      data: data ? JSON.parse(JSON.stringify(data)) : null
    }
    this.logs.push(logEntry)
    console.log(`[${this.componentName}] ${message}`, data)
  }

  // 检查GridStack实例状态
  checkGridStackStatus () {
    this.log('=== GridStack状态检查 ===')

    if (!this.gridStack) {
      this.log('❌ GridStack实例不存在')
      return false
    }

    this.log('✅ GridStack实例存在')
    this.log('GridStack版本信息', {
      version: this.gridStack.version || 'unknown',
      engine: this.gridStack.engine ? 'exists' : 'missing'
    })

    return true
  }

  // 检查DOM状态
  checkDOMStatus (containerElement) {
    this.log('=== DOM状态检查 ===')

    if (!containerElement) {
      this.log('❌ 容器元素不存在')
      return false
    }

    this.log('✅ 容器元素存在')
    this.log('容器信息', {
      tagName: containerElement.tagName,
      className: containerElement.className,
      childrenCount: containerElement.children.length
    })

    // 检查grid-stack-item元素
    const gridItems = containerElement.querySelectorAll('.grid-stack-item')
    this.log(`找到 ${gridItems.length} 个grid-stack-item元素`)

    gridItems.forEach((item, index) => {
      this.log(`Grid Item ${index}`, {
        id: item.id,
        'gs-x': item.getAttribute('gs-x'),
        'gs-y': item.getAttribute('gs-y'),
        'gs-w': item.getAttribute('gs-w'),
        'gs-h': item.getAttribute('gs-h')
      })
    })

    return true
  }

  // 测试addWidget功能
  testAddWidget () {
    this.log('=== 测试addWidget功能 ===')

    if (!this.gridStack) {
      this.log('❌ 无法测试，GridStack实例不存在')
      return false
    }

    try {
      const testWidget = {
        x: 0,
        y: 0,
        w: 2,
        h: 2,
        content: '<div style="background: #f0f0f0; height: 100%; display: flex; align-items: center; justify-content: center;">测试Widget</div>',
        id: `test-${Date.now()}`
      }

      this.log('尝试添加测试widget', testWidget)
      const result = this.gridStack.addWidget(testWidget)
      this.log('addWidget返回结果', result)

      if (result) {
        this.log('✅ addWidget测试成功')

        // 延迟检查DOM
        setTimeout(() => {
          const addedElement = document.querySelector(`[gs-id="${testWidget.id}"]`)
          if (addedElement) {
            this.log('✅ 测试widget已添加到DOM')
            // 清理测试widget
            this.gridStack.removeWidget(addedElement)
            this.log('🧹 测试widget已清理')
          } else {
            this.log('❌ 测试widget未在DOM中找到')
          }
        }, 100)

        return true
      } else {
        this.log('❌ addWidget返回空值')
        return false
      }
    } catch (error) {
      this.log('❌ addWidget测试失败', {
        error: error.message,
        stack: error.stack
      })
      return false
    }
  }

  // 验证图片数据
  validateImageData (imageData) {
    this.log('=== 验证图片数据 ===')

    if (!imageData) {
      this.log('❌ 图片数据为空')
      return false
    }

    const issues = []

    if (!imageData.url) {
      issues.push('缺少url字段')
    } else if (typeof imageData.url !== 'string') {
      issues.push('url字段不是字符串')
    }

    if (!imageData.name) {
      issues.push('缺少name字段')
    }

    if (imageData.sort !== undefined && typeof imageData.sort !== 'number') {
      issues.push('sort字段不是数字')
    }

    if (issues.length > 0) {
      this.log('❌ 图片数据验证失败', { issues, data: imageData })
      return false
    }

    this.log('✅ 图片数据验证通过', imageData)
    return true
  }

  // 生成诊断报告
  generateReport () {
    const report = {
      componentName: this.componentName,
      timestamp: new Date().toISOString(),
      logs: this.logs,
      summary: {
        totalLogs: this.logs.length,
        errors: this.logs.filter(log => log.message.includes('❌')).length,
        successes: this.logs.filter(log => log.message.includes('✅')).length
      }
    }

    console.log('=== GridStack诊断报告 ===')
    console.log(report)

    return report
  }

  // 清空日志
  clearLogs () {
    this.logs = []
    this.log('日志已清空')
  }
}

// 便捷函数
export function debugGridStack (gridStackInstance, containerElement, componentName = 'GridStack') {
  const gridDebugger = new GridStackDebugger(gridStackInstance, componentName)

  gridDebugger.checkGridStackStatus()
  gridDebugger.checkDOMStatus(containerElement)
  gridDebugger.testAddWidget()

  return gridDebugger.generateReport()
}

// 验证图片数据的便捷函数
export function validateImageData (imageData) {
  const validator = new GridStackDebugger(null, 'ImageValidator')
  return validator.validateImageData(imageData)
}

export default GridStackDebugger
