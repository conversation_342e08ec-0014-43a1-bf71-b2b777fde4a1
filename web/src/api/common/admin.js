export function getRolesApi(params) {
  return useGet('/v1/admin/roles',params)
}
export function createRole<PERSON>pi(params) {
  return usePost('/v1/admin/role',params)
}
export function updateRole<PERSON>pi(params) {
  return usePut('/v1/admin/role',params)
}
export function deleteRole<PERSON>pi(params) {
  return useDelete('/v1/admin/role',params)
}
export function getUserPermissionsApi(params) {
  return useGet('/v1/admin/user/permissions',params)
}
export function getRolePermissionsApi(params) {
  return useGet('/v1/admin/role/permissions',params)
}
export function updateRolePermissionsApi(params) {
  return usePut('/v1/admin/role/permission',params)
}

export function getAdminApiApi(params) {
  return useGet('/v1/admin/apis',params)
}
export function createAdminApiApi(params) {
  return usePost('/v1/admin/api',params)
}
export function updateAdminApiApi(params) {
  return usePut('/v1/admin/api',params)
}
export function deleteAdminApiApi(params) {
  return useDelete('/v1/admin/api',params)
}