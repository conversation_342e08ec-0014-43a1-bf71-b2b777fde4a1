// api/messageWork.js
// 创建消息索引的 API 函数
export function createMessageWorkApi(params) {
  return usePost('/v1/messageWork/create', params);
}

// 更新消息索引的 API 函数
export function updateMessageWorkApi(params) {
  return usePut('/v1/messageWork/update', params);
}

// 删除消息索引的 API 函数
export function deleteMessageWorkApi(params) {
  return useDelete('/v1/messageWork/delete', params);
}

// 获取消息索引列表的 API 函数
export function getMessageWorkListApi(params) {
  return useGet('/v1/messageWork/list', params);
}

// 获取消息索引详情的 API 函数
export function getMessageWorkDetailApi(id) {
  return useGet(`/v1/messageWork/detail/${id}`);
}

export function getSignatureApi(params) {
  return useGet('/v1/upyun/signature', params);
}

