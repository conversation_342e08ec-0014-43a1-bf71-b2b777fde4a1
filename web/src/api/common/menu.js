export function getMenusApi() {
  // return useGet('/v1/menus')
  return {
    code: 0,
    message: "ok",
    data: {
      list: [
        {
          id: 2,
          weight: 2,
          path: "/dashboard/messageindex", // 路由路径
          title: "消息索引页", // 菜单标题
          name: "DashboardMessageindex", // 路由名称（唯一）
          component: "/dashboard/messageindex/index", // 指向messageIndex目录下的index.vue
          locale: "作品管理",
          icon: "MessageOutlined", // 或其他合适的图标
          keepAlive: false,
          updatedAt: "2025-05-14 12:00:00",
        },
        
        {
          id: 4, // 假设新增数据的id为4，你可以根据实际情况修改
          weight: 4, // 权重，可按需修改
          path: "/dashboard/messagework", // 新增数据的路径
          title: "消息工作页", // 新增数据的菜单标题，你可以自定义
          name: "DashboardMessagework", // 新增数据的路由名称，需保持唯一
          component: "/dashboard/messagework/index", // 假设对应的组件路径，你可以根据实际情况修改
          locale: "轮播图管理", // 新增数据的国际化文案键名，可按需修改
          icon: "MessageOutlined", // 图标，可按需修改
          keepAlive: false, // 是否缓存，可按需修改
          updatedAt: "2025-05-14 12:00:00", // 更新时间，可按需修改
        },
         {
          id: 5, // 假设新增数据的id为4，你可以根据实际情况修改
          weight: 5, // 权重，可按需修改
          path: "/dashboard/member", // 新增数据的路径
          title: "成员", // 新增数据的菜单标题，你可以自定义
          name: "DashboardMember", // 新增数据的路由名称，需保持唯一
          component: "/dashboard/member/index", // 假设对应的组件路径，你可以根据实际情况修改
          locale: "成员", // 新增数据的国际化文案键名，可按需修改
          icon: "MessageOutlined", // 图标，可按需修改
          keepAlive: false, // 是否缓存，可按需修改
          updatedAt: "2025-05-14 12:00:00", // 更新时间，可按需修改
        },
        {
          id: 6, // 假设新增数据的id为4，你可以根据实际情况修改
          weight: 6, // 权重，可按需修改
          path: "/dashboard/about", // 新增数据的路径
          title: "关于我们", // 新增数据的菜单标题，你可以自定义
          name: "DashboardAbout", // 新增数据的路由名称，需保持唯一
          component: "/dashboard/about/index", // 假设对应的组件路径，你可以根据实际情况修改
          locale: "关于我们", // 新增数据的国际化文案键名，可按需修改
          icon: "MessageOutlined", // 图标，可按需修改
          keepAlive: false, // 是否缓存，可按需修改
          updatedAt: "2025-05-14 12:00:00", // 更新时间，可按需修改
        },
          {
          id: 7,
          weight: 7,
          path: "/dashboard/award", // 路由路径
          title: "消息索引页", // 菜单标题
          name: "DashboardAward", // 路由名称（唯一）
          component: "/dashboard/award/index", // 指向messageIndex目录下的index.vue
          locale: "获奖管理",
          icon: "MessageOutlined", // 或其他合适的图标
          keepAlive: false,
          updatedAt: "2025-05-14 12:00:00",
        },
        
                 {
          id: 9, // 假设新增数据的id为4，你可以根据实际情况修改
          weight: 9, // 权重，可按需修改
          path: "/access/admin", // 新增数据的路径
          title: "账号管理", // 新增数据的菜单标题，你可以自定义
          name: "AccessAdmin", // 新增数据的路由名称，需保持唯一
          component: "/access/admin", // 假设对应的组件路径，你可以根据实际情况修改
          locale: "账号管理", // 新增数据的国际化文案键名，可按需修改
          icon: "MessageOutlined", // 图标，可按需修改
          keepAlive: false, // 是否缓存，可按需修改
          updatedAt: "2025-05-14 12:00:00", // 更新时间，可按需修改
        },

      ],
    },
  };
}
export function getAdminMenusApi() {
  return useGet("/v1/admin/menus");
}
export function createMenuApi(params) {
  return usePost("/v1/admin/menu", params);
}
export function updateMenuApi(params) {
  return usePut("/v1/admin/menu", params);
}
export function deleteMenusApi(params) {
  return useDelete("/v1/admin/menu", params);
}
