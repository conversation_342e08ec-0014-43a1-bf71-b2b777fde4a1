
export function createCategoryApi(params) {
  return usePost('/v1/category/create', params)
}

export function updateCategoryApi(params) {
  return usePut('/v1/category/update', params)
}

export function deleteCategoryApi(params) {
  return useDelete('/v1/category/delete', params)
}

export function getCategoryListApi(params) {
  return useGet('/v1/category/list', params)
}

// export function getMessageIndexDetailApi(id) {
//   // 注意：这里使用路径参数，而非查询参数
//   return useGet(`/v1/messageIndex/detail/${id}`)
// }